/* From Uiverse.io by JeremGamingYT */ 
.loader {
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  inset: 0;
  background: rgba(255,255,255,0.7); /* <PERSON><PERSON><PERSON> chọn, t<PERSON><PERSON> hi<PERSON>u <PERSON>ng mờ nền */
  z-index: 9999;
}


.loader svg {
  margin: 0 5px;
  width: 64px;
  height: 64px;
}

.absolute {
  position: absolute;
}

.inline-block {
  display: inline-block;
}

.w-2 {
  width: 0.5em;
}

/* Animations */
.dash {
  animation:
    dashArray 2s ease-in-out infinite,
    dashOffset 2s linear infinite;
}

.spin {
  animation:
    spinDashArray 2s ease-in-out infinite,
    spin 8s ease-in-out infinite,
    dashOffset 2s linear infinite;
  transform-origin: center;
}

@keyframes dashArray {
  0% {
    stroke-dasharray: 0 1 359 0;
  }
  50% {
    stroke-dasharray: 0 359 1 0;
  }
  100% {
    stroke-dasharray: 359 1 0 0;
  }
}

@keyframes spinDashArray {
  0% {
    stroke-dasharray: 270 90;
  }
  50% {
    stroke-dasharray: 0 360;
  }
  100% {
    stroke-dasharray: 270 90;
  }
}

@keyframes dashOffset {
  0% {
    stroke-dashoffset: 365;
  }
  100% {
    stroke-dashoffset: 5;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  12.5%,
  25% {
    transform: rotate(270deg);
  }
  37.5%,
  50% {
    transform: rotate(540deg);
  }
  62.5%,
  75% {
    transform: rotate(810deg);
  }
  87.5%,
  100% {
    transform: rotate(1080deg);
  }
}

/* Dégradés */
.gradient-b {
  stroke: url(#b);
}

.gradient-c {
  stroke: url(#c);
}

.gradient-d {
  stroke: url(#d);
}


