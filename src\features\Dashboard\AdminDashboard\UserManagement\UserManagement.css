/* UserManagement Component Styles */
.user-management-container {
  padding: 20px;
}

.user-management-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.user-management-tabs .ant-tabs-tab {
  font-weight: 500;
}

.user-management-tabs .ant-tabs-tab-active {
  color: #2753d0;
}

.user-table-container {
  margin-top: 16px;
}

.user-action-buttons {
  display: flex;
  gap: 8px;
}

.user-role-tag {
  font-weight: 500;
  border-radius: 4px;
}

.user-modal-form .ant-form-item-label > label {
  font-weight: 500;
}

.user-modal-form .ant-select-selector,
.user-modal-form .ant-input,
.user-modal-form .ant-picker {
  border-radius: 6px;
}

.user-modal-form .ant-select-selector:focus,
.user-modal-form .ant-input:focus,
.user-modal-form .ant-picker:focus {
  border-color: #2753d0;
  box-shadow: 0 0 0 2px rgba(39, 83, 208, 0.1);
}

.specialization-field-container {
  animation: fadeIn 0.3s ease-in-out;
}

/* SpecializationCell Styles */
.specialization-cell-not-applicable {
  color: #999;
}

.specialization-cell-container {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.specialization-cell-content {
  flex: 1;
}

.specialization-cell-loading,
.specialization-cell-empty {
  color: #999;
}

.specialization-cell-tag {
  margin-bottom: 2px;
  margin-right: 4px;
}

.specialization-cell-add-btn {
  padding: 2px 4px;
  height: 24px;
  min-width: 24px;
  color: #1890ff;
  margin-left: 8px;
}

/* Modal Styles */
.specialization-modal-user-info {
  margin-bottom: 16px;
}

.specialization-modal-label {
  display: block;
  margin-bottom: 8px;
  font-weight: bold;
}

.specialization-modal-select {
  width: 100%;
}

.specialization-modal-preview {
  margin-top: 16px;
}

.specialization-modal-preview-title {
  margin-bottom: 8px;
  font-weight: bold;
}

.specialization-modal-preview-tag {
  margin-bottom: 4px;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Image Upload Styles */
.image-upload-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.image-preview {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 8px;
  border: 1px solid #d9d9d9;
  transition: all 0.3s ease;
}

.image-preview:hover {
  border-color: #2753d0;
  box-shadow: 0 2px 8px rgba(39, 83, 208, 0.15);
}

.upload-button {
  border-color: #2753d0;
  color: #2753d0;
}

.upload-button:hover {
  border-color: #086ce4;
  color: #086ce4;
}
