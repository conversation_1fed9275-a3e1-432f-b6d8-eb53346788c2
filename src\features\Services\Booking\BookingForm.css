/* BookingForm.css */
.appointment-card {
  width: 100%;
  max-width: 480px;
  margin: 0 auto;
  padding: 20px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  background-color: #ffffff;
  box-sizing: border-box;
}

.appointment-header {
  margin-bottom: 16px;
}

.appointment-header h3 {
  margin: 0 0 6px 0;
  font-size: 20px;
  font-weight: 600;
  color: #222;
}

.location-info {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  color: #666;
  font-size: 13px;
  line-height: 1.4;
}

.location-icon {
  color: #1890ff;
  font-size: 14px;
  margin-top: 2px;
  flex-shrink: 0;
}

.service-info-section {
  margin-bottom: 20px;
}

.service-info-section h4 {
  margin-bottom: 4px;
  font-weight: 600;
}

.form-section {
  margin-bottom: 5px;
}

.full-width-select,
.date-range-picker {
  width: 100% !important;
  margin-top: 8px;
}

.date-range-picker .ant-picker {
  height: 40px;
  border-radius: 8px;
  width: 100%;
}

.schedule-section {
  margin-bottom: 24px;
}

.day-selector {
  display: flex;
  align-items: center;
  gap: 8px;
  /* margin-bottom: 16px; */
}

.days-container {
  display: flex;
  gap: 8px;
  flex: 1;
  overflow-x: auto;
  padding: 2px;
}

.day-card {
  min-width: 58px;
  text-align: center;
  padding: 6px 4px;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
  background: white;
  font-size: 14px;
  cursor: pointer;
  user-select: none;
  transition: all 0.2s;
}

.day-card:hover:not(.disabled) {
  border-color: #1890ff;
  box-shadow: 0 1px 4px rgba(24, 144, 255, 0.1);
}

.day-card.selected {
  background-color: #1890ff;
  color: white;
  border-color: #1890ff;
}

.day-card.disabled {
  opacity: 0.4;
  cursor: not-allowed;
  background-color: #f5f5f5;
}

.time-tabs {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
}

.time-tabs .ant-tabs-nav {
  margin-bottom: 8px;
  justify-content: center;
  display: flex;
  width: 100%;
}

.time-tabs .ant-tabs-nav .ant-tabs-nav-list {
  margin: 0 auto;
  display: flex;
  justify-content: center;
  width: 100%;
}

.time-tabs .ant-tabs-tab {
  padding: 4px 10px;
  font-size: 15px;
  font-weight: 500;
}

.time-slots-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.time-slot {
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
  background-color: white;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 12px;
  text-align: center;
  min-width: 120px;
  min-height: 50px;
}

.time-slots-grid .time-slot:hover {
  border-color: #1890ff !important;
  color: #1890ff;
}

.time-slot.selected {
  background-color: #1890ff;
  color: white;
  border-color: #1890ff;
}

.time-slot.selected .slot-available {
  color: #a8d8a8; /* Lighter green for selected state */
}

.time-slot-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.time-display {
  font-weight: 500;
  font-size: 16px;
}

.slot-available {
  font-size: 14px;
  color: #52c41a;
  font-weight: 450;
}

@media (max-width: 480px) {
  .appointment-card {
    max-width: 100%;
    margin: 0;
    padding: 16px;
  }

  .time-slots-grid {
    grid-template-columns: 1fr;
  }

  .day-card {
    min-width: 50px;
    padding: 6px 4px;
  }
}
.slot-count {
  font-size: 14px;
  color: #52c41a;
}
.booking-price {
  text-align: start;
  font-size: 20px;
  margin: 8px 0 8px;
}
.booking-price span {
  font-weight: 600;
}
.price-highlight {
  color: #38a169; /* màu xanh như ảnh */
  font-size: 22px;
  margin-left: 4px;
}

/* CSS cho form labels */
.form-label {
  margin-top: 16px;
  display: block;
}

/* CSS cho consultant selection notification */
.consultant-selected-notification {
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 6px;
  padding: 8px 12px;
  margin-bottom: 8px;
  font-size: 14px;
  color: #0050b3;
}

/* CSS cho consultant select dropdown */
.consultant-select {
  width: 100%;
}
