/* Customer Chat Widget - Medical Theme Styling */

/* Chat Widget Button */
.chat-widget-button {
  position: fixed;
  bottom: 24px;
  right: 24px;
  z-index: 1000;
  cursor: pointer;
}

.chat-toggle-btn {
  width: 64px !important;
  height: 64px !important;
  font-size: 24px !important;
  background: linear-gradient(135deg, #1890ff, #40a9ff) !important;
  border: none !important;
  box-shadow: 0 8px 24px rgba(24, 144, 255, 0.4) !important;
  transition: all 0.3s ease !important;
  color: white !important;
}

.chat-toggle-btn:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 12px 32px rgba(24, 144, 255, 0.6) !important;
  background: linear-gradient(135deg, #096dd9, #1890ff) !important;
}

.chat-toggle-btn.open {
  background: linear-gradient(135deg, #52c41a, #73d13d) !important;
}

/* Unread Count Badge - Messenger Style */
.chat-widget-button .ant-badge-count {
  background: #ff4d4f !important;
  color: white !important;
  font-weight: bold !important;
  font-size: 12px !important;
  min-width: 20px !important;
  height: 20px !important;
  line-height: 20px !important;
  border-radius: 10px !important;
  border: 2px solid white !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
  animation: pulse 2s infinite !important;
}

/* Pulse animation for unread badge */
@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(255, 77, 79, 0.4);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
}

/* Hide badge when count is 0 */
.chat-widget-button .ant-badge-count[title="0"] {
  display: none !important;
}

/* Chat Widget Panel */
.chat-widget-panel {
  position: fixed;
  bottom: 100px;
  right: 24px;
  width: 400px;
  height: 600px;
  z-index: 999;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.3);
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.chat-widget-container {
  height: 100%;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  border: 1px solid #e8e8e8;
}

/* Chat Header */
.chat-header {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  padding: 16px;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
}

.chat-header-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.chat-header-info h4 {
  color: white !important;
  margin: 0;
  font-weight: 600;
}

.chat-status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.chat-status-indicator span {
  color: rgba(255, 255, 255, 0.9);
  font-size: 12px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #52c41a;
  animation: pulse 2s infinite;
}

.status-dot.offline {
  background: #d9d9d9;
  animation: none;
}

.status-dot.waiting {
  background: #faad14;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(82, 196, 26, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0);
  }
}

/* Messages Area */
.chat-messages-area {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background: #fafafa;
  scroll-behavior: smooth;
}

.chat-messages-area::-webkit-scrollbar {
  width: 6px;
}

.chat-messages-area::-webkit-scrollbar-track {
  background: #f0f0f0;
}

.chat-messages-area::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 3px;
}

.chat-messages-area::-webkit-scrollbar-thumb:hover {
  background: #bfbfbf;
}

/* Message Wrapper */
.message-wrapper {
  margin-bottom: 16px;
}

.message-content {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.message-details {
  flex: 1;
  min-width: 0;
}

.message-header {
  display: flex;
  align-items: baseline;
  margin-bottom: 4px;
  gap: 8px;
}

.message-header .ant-typography {
  color: #1890ff !important;
  font-weight: 600;
  margin: 0;
}

.message-text {
  word-wrap: break-word;
  line-height: 1.5;
  /* Colors controlled by JavaScript */
  padding: 12px 16px;
  border-radius: 18px;
  border: 1px solid #e8e8e8;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
}

/* Typing Indicator */
.typing-indicator {
  display: flex;
  gap: 12px;
  align-items: center;
  margin-bottom: 16px;
  opacity: 0.8;
}

.typing-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.typing-dots {
  display: flex;
  gap: 4px;
}

.typing-dots span {
  width: 6px;
  height: 6px;
  background: #5865f2;
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Input Area */
.chat-input-area {
  background: white;
  padding: 16px;
  border-top: 1px solid #e8e8e8;
}

.chat-input-container {
  display: flex;
  align-items: flex-end;
  gap: 8px;
}

.chat-input-container .ant-input {
  background: #fafafa !important;
  border: 1px solid #d9d9d9 !important;
  color: #262626 !important;
  border-radius: 20px !important;
  padding: 8px 16px !important;
}

.chat-input-container .ant-input:focus {
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

.chat-input-container .ant-input::placeholder {
  color: #8c8c8c !important;
}

.chat-input-container .ant-btn {
  background: #1890ff !important;
  border-color: #1890ff !important;
  border-radius: 50% !important;
  width: 40px !important;
  height: 40px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.chat-input-container .ant-btn:hover {
  background: #40a9ff !important;
  border-color: #40a9ff !important;
}

/* Responsive */
@media (max-width: 480px) {
  .chat-widget-panel {
    right: 16px;
    left: 16px;
    width: auto;
    bottom: 90px;
  }

  .chat-widget-button {
    right: 16px;
    bottom: 16px;
  }

  .chat-toggle-btn {
    width: 56px !important;
    height: 56px !important;
    font-size: 20px !important;
  }
}

/* Medical theme overrides for Ant Design components */
.chat-widget-container .ant-btn-text {
  color: #1890ff !important;
}

.chat-widget-container .ant-btn-text:hover {
  color: #40a9ff !important;
  background: rgba(24, 144, 255, 0.1) !important;
}

.chat-widget-container .ant-typography {
  color: #262626 !important;
}

/* Close button styling */
.chat-header .ant-btn-text {
  color: rgba(255, 255, 255, 0.9) !important;
}

.chat-header .ant-btn-text:hover {
  color: white !important;
  background: rgba(255, 255, 255, 0.1) !important;
}

/* Animation for smooth interactions */
.chat-widget-container * {
  transition: all 0.2s ease;
}

/* Focus states */
.chat-input-container .ant-input:focus,
.chat-input-container .ant-btn:focus {
  outline: none;
}

/* Name Form Styling */
.chat-name-form {
  padding: 24px;
  text-align: center;
  background: white;
}

.chat-name-form h4 {
  color: #1890ff !important;
  margin-bottom: 16px;
  font-weight: 600;
}

.chat-name-form .ant-input {
  border-radius: 8px !important;
  border-color: #d9d9d9 !important;
  padding: 12px 16px !important;
  margin-bottom: 16px;
}

.chat-name-form .ant-input:focus {
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

.chat-name-form .ant-btn-primary {
  background: #1890ff !important;
  border-color: #1890ff !important;
  border-radius: 8px !important;
  height: 40px !important;
  font-weight: 600;
}

.chat-name-form .ant-btn-primary:hover {
  background: #40a9ff !important;
  border-color: #40a9ff !important;
}

/* Message colors handled by JavaScript */

/* Waiting Area Styling */
.chat-waiting-area {
  padding: 20px;
  text-align: center;
  background: white;
  border-top: 1px solid #e8e8e8;
}

.waiting-message {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 16px;
  background: #f0f9ff;
  border: 1px solid #bae7ff;
  border-radius: 8px;
  color: #1890ff;
}

.waiting-message .ant-typography {
  margin: 0 !important;
  color: #8c8c8c !important;
}

/* Message hover effects */
.message-wrapper:hover {
  background: rgba(255, 255, 255, 0.02);
  border-radius: 8px;
  margin: -4px;
  padding: 4px;
}

/* System messages */
.message-wrapper.system-message {
  text-align: center;
  margin: 12px 0;
}

.message-wrapper.system-message .message-content {
  justify-content: center;
  background: rgba(88, 101, 242, 0.1);
  border: 1px solid rgba(88, 101, 242, 0.3);
  border-radius: 16px;
  padding: 8px 16px;
  margin: 0 20px;
}

.message-wrapper.system-message .message-text {
  color: #5865f2 !important;
  font-size: 13px;
  font-weight: 500;
  text-align: center;
}

.message-wrapper.system-message .message-avatar {
  display: none;
}
