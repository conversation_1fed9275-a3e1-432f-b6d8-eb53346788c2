/* ===== HEADER MAIN STYLES ===== */
.header {
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: var(--z-sticky);
  background: var(--bg-white);
  transition: all 0.3s ease;
  border-radius: 0;
  box-shadow: none;
}

.header.scrolled {
  margin: 10px 20px 0 20px;
  width: calc(100% - 40px);
  border-radius: 50px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.12);
  overflow: hidden;
  background: #fff !important;
}

@media (max-width: 768px) {
  .header.scrolled {
    margin: 5px 10px 0 10px;
    width: calc(100% - 20px);
    border-radius: 12px;
  }
}
