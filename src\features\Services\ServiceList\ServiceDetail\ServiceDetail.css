.service-detail-container {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  align-items: flex-start;
  justify-content: space-between;
  padding: 24px 24px 32px;
  max-width: 1200px;
  margin: 0 auto;
  background-color: #fff;
  border-radius: 8px;
  height: auto;
}

.service-info-detail {
  width: 50%;
}

/* <PERSON>ên tr<PERSON>i chứa thông tin dịch vụ */
.service-detail-left {
  flex: 1;
  max-width: calc(100% - 440px);
  /* giữ đủ chỗ cho bên phải */
  min-width: 0;
}

.service-detail-right {
  width: 400px;
  flex-shrink: 0;
}

/* <PERSON><PERSON>n ph<PERSON>i là form đặt lịch */
.service-detail-right {
  width: 400px;
  flex-shrink: 0;
}

.service-title {
  font-size: 22px;
  font-weight: 700;
  margin-bottom: 8px;
  color: #1e293b;
}

.service-price {
  color: #10b981;
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 16px;
}

.service-description,
.service-preparation,
.service-detail-list {
  margin-bottom: 24px;
}

.service-details-wrapper {
  padding: 8px 0;
}

.service-description h3,
.prep-section h3 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #0f172a;
}

.service-description .description {
  margin-bottom: 24px;
  color: #334155;
  line-height: 1.6;
}

.prep-items {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 16px;
}

.prep-item {
  border-radius: 8px;
  font-size: 14px;
  line-height: 1.6;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
}

/* Responsive: Stack dọc trên mobile */
@media (max-width: 768px) {
  .service-detail-container {
    flex-direction: column;
    padding: 16px;
  }

  .service-detail-right {
    width: 100%;
    max-width: 100%;
  }
}

/* Tab items */

.ant-tabs-tab {
  padding: 12px 24px !important;
  font-size: 16px;
  font-weight: 600;
  color: #334155;
}

.ant-tabs-tab-active {
  background-color: #e0f7fa;
  border-radius: 8px;
  color: #0288d1 !important;
}

.ant-tabs-nav,
.ant-tabs-nav-wrap {
  justify-content: flex-start !important;
}

.ant-tabs-ink-bar {
  height: 4px;
  background-color: #0288d1 !important;
  border-radius: 4px;
}

/* Thêm CSS cho phần đánh giá */
.star-rating {
  display: inline-flex;
  margin-right: 8px;
}

.star {
  color: #e0e0e0;
  font-size: 24px;
}

.star.filled {
  color: #ffd700;
}

/* CSS cho danh sách đánh giá */
.feedback-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 16px 0;
}

.feedback-card {
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.feedback-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.feedback-author {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.feedback-date {
  font-size: 14px;
  color: #6b7280;
  margin: 0 0 12px 0;
}

.feedback-comment {
  font-size: 15px;
  color: #374151;
  line-height: 1.6;
  margin: 0;
}

/* CSS cho danh sách bác sĩ */
.consultants-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 8px 0;
}

.consultant-card {
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.consultant-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.consultant-card:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.consultant-card .ant-card-body {
  padding: 24px;
}

.consultant-info {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  position: relative;
}

.consultant-name {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.consultant-specialization {
  margin: 4px 0;
  color: #2753d0;
  font-weight: 500;
}

.consultant-email {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.consultant-details {
  flex: 1;
  min-width: 0;
}

.consultant-actions {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: 30px;
  margin-left: auto;
  flex-shrink: 0;
}

.select-consultant-btn {
  background: linear-gradient(135deg, #2753d0 0%, #086ce4 100%);
  border: none;
  border-radius: 8px;
  font-weight: 600;
  font-size: 12px;
  height: 32px;
  padding: 0 16px;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.select-consultant-btn:hover {
  background: linear-gradient(135deg, #086ce4 0%, #0283f5 100%);
  transform: translateY(-1px);
}

.select-consultant-btn:active {
  transform: translateY(0);
}

/* CSS cho modal bác sĩ */
.consultant-modal-content {
  display: flex;
  gap: 24px;
  align-items: flex-start;
  background: white;
  padding: 16px;
  border-radius: 8px;
}

.consultant-modal-left {
  flex-shrink: 0;
  text-align: center;
}

.consultant-modal-avatar {
  border: 2px solid #e3f2fd;
  border-radius: 8px;
}

.consultant-modal-right {
  flex: 1;
}

.consultant-modal-name {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 700;
  color: #374151;
}

.consultant-modal-specialization {
  margin: 0 0 20px 0;
  color: #6b7280;
  font-size: 15px;
  font-weight: 500;
  font-style: italic;
  padding: 8px 12px;
  background: #f8fafc;
  border-radius: 8px;
}

.consultant-modal-buttons {
  display: flex;
  gap: 12px;
  margin: 16px 0;
}

.consultant-modal-btn-primary {
  background: #2196f3;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.consultant-modal-btn-secondary {
  background: transparent;
  color: #757575;
  border: 1px solid #e0e0e0;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.consultant-modal-btn-primary:hover {
  background: #1976d2;
  transform: translateY(-1px);
}

.consultant-modal-btn-secondary:hover {
  border-color: #2196f3;
  color: #2196f3;
}

.consultant-modal-email,
.consultant-modal-phone,
.consultant-modal-experience,
.consultant-modal-description {
  margin: 8px 0;
  color: #374151;
  line-height: 1.5;
}

.consultant-modal-description {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

/* CSS cho empty states */
.empty-state {
  text-align: center;
  padding: 32px 0;
}

.empty-state-text {
  color: #888;
  margin-top: 12px;
}

/* CSS cho consultant card clickable */
.consultant-card-clickable {
  cursor: pointer;
}

/* CSS cho specialization tags */
.consultant-specializations {
  margin: 8px 0;
}

.specialization-tag {
  display: inline-block;
  background-color: #e0f7fa;
  color: #0288d1;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  margin-right: 6px;
  margin-bottom: 4px;
}

.consultant-rating {
  display: flex;
  align-items: center;
  margin: 8px 0;
}

.rating-text {
  margin-left: 8px;
  color: #6b7280;
  font-size: 14px;
}

.consultant-gender,
.consultant-phone {
  margin: 4px 0;
  color: #374151;
  font-size: 14px;
}

/* CSS cho modal bác sĩ - cập nhật */
.consultant-modal-rating {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 12px;
}

.consultant-modal-specializations {
  margin: 16px 0;
}

.specializations-list {
  margin-top: 8px;
}

.specialization-tag-modal {
  display: inline-block;
  background-color: #e0f7fa;
  color: #0288d1;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  margin-right: 8px;
  margin-bottom: 6px;
}

.consultant-modal-info {
  margin: 12px 0;
  color: #374151;
  line-height: 1.5;
}

.consultant-modal-certifications {
  margin: 16px 0;
  padding-top: 16px;
}

.certifications-list {
  margin-top: 12px;
}

.certification-item {
  display: flex;
  align-items: center;
  padding: 8px;
  border-radius: 6px;
}

.cert-name {
  font-weight: 500;
  color: #374151;
}

.cert-image {
  border-radius: 4px;
  border: 1px solid #e5e7eb;
}

.no-info {
  color: #9ca3af;
  font-style: italic;
}

/* CSS cho modal title với thông tin bác sĩ */
.modal-title-content {
  max-width: 100%;
  padding: 0;
}

.modal-title-content h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 700;
  color: #333333;
  text-align: center;
}

.doctor-highlights {
  text-align: left;
  background: linear-gradient(135deg, #e3f2fd 0%, #f0f8ff 100%);
  padding: 15px;
  border-radius: 8px;
}

.doctor-highlights p {
  margin: 16px 0;
  line-height: 1.7;
  color: #37474f;
  font-size: 14px;
  position: relative;
  padding-left: 20px;
}

.doctor-highlights p:before {
  content: "•";
  color: #2196f3;
  font-weight: bold;
  position: absolute;
  left: 0;
  font-size: 16px;
}

.doctor-highlights p strong {
  color: #333333;
  font-weight: 700;
}

.doctor-highlights p:first-child {
  margin-top: 0;
}

.doctor-highlights p:last-child {
  margin-bottom: 0;
}

/* CSS cho highlights section ở bottom */

.modal-highlights-bottom h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 700;
  color: #333333;
}
