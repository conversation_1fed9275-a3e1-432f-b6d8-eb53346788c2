/* ===== AUTH BUTTONS STYLES ===== */
.auth-buttons {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
}

.auth-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  font-weight: 500;
  font-size: 14px;
  padding: var(--spacing-xs) var(--spacing-lg);
  border-radius: var(--radius-md);
  transition: all var(--transition-normal);
  min-width: 80px;
  text-align: center;
}

/* ===== LOGIN BUTTON ===== */

/* ===== REGISTER BUTTON ===== */
.register-btn {
  color: var(--text-white);
  background: var(--gradient-primary);
  border: 1px solid transparent;
  box-shadow: var(--shadow-sm);
}

.register-btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* ===== USER DROPDOWN MENU STYLES ===== */
.ant-dropdown-menu {
  min-width: 200px !important;
  padding: 8px 0 !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.ant-dropdown-menu-item {
  padding: 12px 16px !important;
  font-size: 14px !important;
  line-height: 1.4 !important;
  min-height: 44px !important;
  display: flex !important;
  align-items: center !important;
}

.ant-dropdown-menu-item .anticon {
  font-size: 16px !important;
  margin-right: 12px !important;
  color: #666 !important;
}

.ant-dropdown-menu-item:hover {
  background-color: #f5f5f5 !important;
}

.ant-dropdown-menu-item:hover .anticon {
  color: #2753d0 !important;
}

/* Đăng xuất button có màu đỏ */
.ant-dropdown-menu-item:last-child:hover {
  background-color: #fff2f0 !important;
}

.ant-dropdown-menu-item:last-child:hover .anticon {
  color: #ff4d4f !important;
}

/* ===== RESPONSIVE STYLES ===== */
@media (max-width: 768px) {
  .auth-buttons {
    display: none;
  }
}
