/* Personal Schedule Component Styles */

.personal-schedule-container {
  padding: 10px;
}

.personal-schedule-header {
  margin-bottom: 24px;
}

.personal-schedule-title {
  margin: 0;
  font-size: 19px;
  color: #1890ff;
}

.personal-schedule-subtitle {
  color: #666;
  margin: 8px 0 0 0;
}

/* Statistics Cards */
.statistics-row {
  margin-bottom: 16px;
}

.statistics-card {
  text-align: center;
}

.statistics-card-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.statistics-icon {
  font-size: 18px;
}

.statistics-icon.total {
  color: #1890ff;
}

.statistics-icon.checked {
  color: #f4af24;
}

.statistics-icon.waiting {
  color: #f46d0b;
}

.statistics-icon.completed {
  color: #52c41a;
}

.statistics-text {
  font-size: 16px;
  font-weight: 500;
}

.statistics-text.total {
  color: #1890ff;
}

.statistics-text.checked {
  color: #f4af24;
}

.statistics-text.waiting {
  color: #f46d0b;
}

.statistics-text.completed {
  color: #52c41a;
}

/* Date Picker Section */
.date-picker-card {
  margin-bottom: 16px;
}

.date-picker-container {
  display: flex;
  align-items: center;
  gap: 16px;
}

.date-picker-label {
  font-weight: bold;
}

.date-picker-input {
  width: 200px;
}

.date-picker-info {
  color: #666;
}

.debug-button {
  margin-left: auto;
}

/* Debug Panel */
.debug-panel-card {
  margin-bottom: 16px;
  border-color: #1890ff;
}

.debug-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.debug-panel-title {
  margin: 0;
  color: #1890ff;
}

.debug-panel-content {
  font-size: 12px;
}

.debug-panel-raw-data {
  font-size: 11px;
  max-height: 150px;
  overflow: auto;
  background: #f5f5f5;
  padding: 8px;
}

/* Table Columns */
.patient-info-name {
  font-weight: bold;
  color: #1890ff;
  font-size: 14px;
}

.patient-info-date {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.patient-info-appointment {
  font-size: 12px;
  color: #666;
}

.patient-detail-button-container {
  margin-top: 6px;
}

.status-description {
  font-size: 11px;
  color: #999;
  margin-top: 2px;
}

.service-name {
  font-weight: bold;
  font-size: 14px;
}

.service-time {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.service-consultant {
  font-size: 12px;
  color: #666;
}

/* Action Buttons */
.action-button-consulting {
  background-color: #52c41a;
  border-color: #52c41a;
}

.action-button-waiting {
  background-color: #fa8c16;
  border-color: #fa8c16;
}

.action-button-result {
  background-color: #52c41a;
  border-color: #52c41a;
}

.completed-status {
  color: #52c41a;
  font-size: 12px;
  font-weight: bold;
}

/* Empty State */
.empty-state-container {
  padding: 40px;
  text-align: center;
}

.empty-state-icon {
  font-size: 48px;
  color: #ccc;
  margin-bottom: 16px;
}

.empty-state-message {
  color: #999;
}

.empty-state-hint {
  color: #ccc;
  font-size: 12px;
  margin-top: 8px;
}
