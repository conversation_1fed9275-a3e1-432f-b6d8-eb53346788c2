@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');

.fp-otp-page {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  font-family: 'Roboto', sans-serif;
  box-sizing: border-box;
}

.fp-otp-card {
  background: #ffffff;
  padding: 30px 40px;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  width: 100%;
  max-width: 500px;
  text-align: center;
  overflow: hidden;
}

.fp-otp-card-header {
  margin-bottom: 25px;
}

.fp-otp-card-title {
  font-size: 28px;
  font-weight: 700;
  color: #333;
  margin: 0;
}

.fp-otp-steps {
  margin-bottom: 35px !important;
  padding: 0 10px;
}

.fp-otp-steps .ant-steps-item-finish .ant-steps-item-icon {
  background-color: #52c41a;
  border-color: #52c41a;
}
.fp-otp-steps .ant-steps-item-finish .ant-steps-item-icon > .ant-steps-icon {
  color: white;
}
.fp-otp-steps .ant-steps-item-process .ant-steps-item-icon {
  background-color: #1890ff;
  border-color: #1890ff;
}
.fp-otp-steps .ant-steps-item-process .ant-steps-item-icon > .ant-steps-icon {
  color: white;
}
.fp-otp-steps .ant-steps-item-wait .ant-steps-item-icon {
  border-color: #bfbfbf;
  background-color: #f0f0f0;
}
.fp-otp-steps .ant-steps-item-title {
  font-weight: 500;
}
.fp-otp-steps .ant-steps-item-finish > .ant-steps-item-container > .ant-steps-item-content > .ant-steps-item-title::after {
  background-color: #52c41a;
}

.fp-otp-step-content {
  margin-top: 20px;
}

.fp-otp-step-description {
  margin-bottom: 25px;
  color: #555;
  font-size: 15px;
  line-height: 1.7;
  text-align: center;
}

.fp-otp-form .ant-form-item-label > label {
  font-weight: 500;
  color: #444;
  font-size: 14px;
}

.fp-otp-input,
.fp-otp-input .ant-input {
  border-radius: 6px !important;
  padding: 12px 15px !important;
  font-size: 16px !important;
  border: 1px solid #d9d9d9 !important;
}
.fp-otp-input:focus, .fp-otp-input:hover,
.fp-otp-input .ant-input:focus, .fp-otp-input .ant-input:hover {
  border-color: #4A90E2 !important;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2) !important;
}

.fp-otp-gradient-button,
.fp-otp-gradient-button.ant-btn-primary {
  color: white !important;
  border: none !important;
  border-radius: 25px !important;
  padding: 12px 24px !important;
  font-size: 16px !important;
  font-weight: bold !important;
  cursor: pointer !important;
  background-image: linear-gradient(to right, #4A90E2 0%, #7873F5 50%, #D27CF8 100%) !important;
  background-size: 200% auto !important;
  transition: background-position 0.5s ease, transform 0.15s ease !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
}
.fp-otp-gradient-button:hover:not(:disabled),
.fp-otp-gradient-button.ant-btn-primary:hover:not(:disabled) {
  background-position: right center !important;
  color: white !important;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15) !important;
  transform: translateY(-2px);
}
.fp-otp-gradient-button:active:not(:disabled),
.fp-otp-gradient-button.ant-btn-primary:active:not(:disabled) {
  transform: translateY(0px);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1) !important;
}
.fp-otp-gradient-button:disabled,
.fp-otp-gradient-button.ant-btn-primary:disabled {
  background-image: none !important;
  background-color: #f5f5f5 !important;
  color: rgba(0, 0, 0, 0.25) !important;
  cursor: not-allowed !important;
  box-shadow: none !important;
}

.fp-otp-secondary-button,
.fp-otp-secondary-button.ant-btn {
  border-radius: 25px !important;
  padding: 12px 24px !important;
  font-size: 16px !important;
  font-weight: 500 !important;
  border: 1px solid #4A90E2 !important;
  color: #4A90E2 !important;
  background-color: white !important;
  transition: background-color 0.3s ease, color 0.3s ease, transform 0.15s ease !important;
}
.fp-otp-secondary-button:hover:not(:disabled),
.fp-otp-secondary-button.ant-btn:hover:not(:disabled) {
  background-color: #e9f2fc !important;
  color: #3a7bd5 !important;
  transform: translateY(-2px);
}

.fp-otp-form-actions .ant-space-item {
  flex-grow: 1;
}
.fp-otp-form-actions .ant-space-item > .ant-btn {
  width: 100%;
}

.fp-otp-back-link,
.fp-otp-resend-link {
  color: #4A90E2 !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  padding-top: 10px !important;
}
.fp-otp-back-link:hover,
.fp-otp-resend-link:hover {
  color: #3a7bd5 !important;
  text-decoration: underline !important;
}

.ant-spin-fullscreen {
  background-color: rgba(255, 255, 255, 0.7) !important;
}
