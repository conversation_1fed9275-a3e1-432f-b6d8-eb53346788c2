import { Link, useLocation } from "react-router-dom";
import { useSelector } from "react-redux";
import "./Navigation.css";

// Removed blog options - no longer using tag-based navigation

const Navigation = () => {
  const location = useLocation();
  const userState = useSelector((state) => state.user);

  // Get user from Redux state or fallback to localStorage
  let user = userState?.user;
  if (!user || !user.email) {
    try {
      const localUser = localStorage.getItem("user");
      if (localUser) {
        user = JSON.parse(localUser);
      }
    } catch {
      console.log("No valid localStorage user data");
    }
  }

  // Base navigation items
  const baseNavigationItems = [
    { label: "Trang chủ", href: "/" },
    { label: "Tin tức", href: "/blog" },
    { label: "Dịch vụ", href: "/services", dropdown: true },
    { label: "<PERSON>ên hệ", href: "/contact" },
  ];

  // Role-specific navigation items
  const getRoleSpecificItems = (userRole) => {
    switch (userRole) {
      case "ADMIN":
        return [{ label: "Người quản lý", href: "/admin" }];
      case "STAFF":
        return [{ label: "Nhân viên", href: "/staff" }];
      case "CONSULTANT":
        return [{ label: "Bác sĩ", href: "/consultant" }];
      default:
        return [];
    }
  };

  // All possible role navigation items (for styling check)
  const allRoleNavigationItems = [
    { label: "Admin", href: "/admin" },
    { label: "Staff", href: "/staff" },
    { label: "Consultant", href: "/consultant" },
  ];

  // Combine navigation items based on user role
  const roleSpecificItems = getRoleSpecificItems(user?.role);
  const navigationItems = [...baseNavigationItems, ...roleSpecificItems];

  return (
    <nav className="main-nav">
      <ul className="nav-list">
        {navigationItems.map((item, index) => {
          // Check if this is a role navigation item
          const isRoleItem = allRoleNavigationItems.some(
            (roleItem) => roleItem.href === item.href
          );

          return (
            <li
              key={index}
              className={`nav-item${item.dropdown ? " has-dropdown" : ""}${
                isRoleItem ? " admin-item" : ""
              }`}
            >
              <Link
                to={item.href}
                className={`nav-link${
                  location.pathname === item.href ? " active" : ""
                }${isRoleItem ? " admin-nav" : ""}`}
              >
                {item.label}
              </Link>

              {/* Removed Tin tức dropdown - now goes directly to /blog */}
            </li>
          );
        })}
      </ul>
    </nav>
  );
};

export default Navigation;
