.ant-layout-header {
  padding: 0 20px;
  background: #2753d0 !important;
}

.ant-layout-sider {
  background: #fff;
}

.ant-layout-content {
  margin: 24px 16px;
  padding: 24px;
  min-height: 280px;
}

/* Card styles */
.ant-card {
  margin-bottom: 16px;
}

.ant-card-head {
  border-bottom: 1px solid #f0f0f0;
}

/* Table styles */
.ant-table-wrapper {
  background: white;
  border-radius: 8px;
}

/* List styles */
.ant-list-item {
  padding: 16px;
}

/* Tag styles */
.ant-tag {
  margin-right: 8px;
}

/* Button styles */
.ant-btn {
  margin-right: 8px;
}

/* Form styles */
.ant-form-item {
  margin-bottom: 16px;
}

/* Modal styles */
.ant-modal-content {
  border-radius: 8px;
}

/* Tabs styles */
.ant-tabs-nav {
  margin-bottom: 16px;
}

.ant-tabs-tab {
  padding: 12px 16px;
}

/* Menu theme override */
.ant-menu.ant-menu-dark {
  background: #2753d0 !important;
  height: 64px !important;
  line-height: 64px !important;
}

.ant-menu.ant-menu-horizontal {
  border-bottom: none !important;
}

.ant-menu.ant-menu-horizontal.ant-menu-dark {
  background: #2753d0 !important;
  height: 64px !important;
}

.ant-menu.ant-menu-dark .ant-menu-item {
  background: #2753d0 !important;
  color: #ffffff !important;
  height: 64px !important;
  line-height: 64px !important;
  margin: 0 !important;
  border-bottom: none !important;
}

.ant-menu.ant-menu-dark .ant-menu-item a {
  color: #ffffff !important;
  height: 64px !important;
  line-height: 64px !important;
  display: flex !important;
  align-items: center !important;
}

.ant-menu.ant-menu-dark .ant-menu-item:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  color: #ffffff !important;
  border-bottom: none !important;
}

.ant-menu.ant-menu-dark .ant-menu-item:hover a {
  color: #ffffff !important;
}

.ant-menu.ant-menu-dark .ant-menu-item-selected {
  background: #1e40af !important;
  color: #ffffff !important;
  border-bottom: 3px solid #ffffff !important;
  height: 64px !important;
  line-height: 61px !important;
}

.ant-menu.ant-menu-dark .ant-menu-item-selected a {
  color: #ffffff !important;
  font-weight: 600 !important;
  height: 61px !important;
  line-height: 61px !important;
}

.ant-menu.ant-menu-dark .ant-menu-item-selected .anticon {
  color: #ffffff !important;
}

/* Menu item text styling */
.ant-menu.ant-menu-dark .ant-menu-item .anticon {
  color: #ffffff !important;
}

/* Header title styling */
.ant-layout-header .ant-typography {
  color: #ffffff !important;
}

.ant-layout-header h3 {
  color: #ffffff !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .ant-card {
    margin-bottom: 12px;
  }

  .ant-list-item {
    padding: 12px;
  }
}
