.breadcrumb-nav {
  margin: 16px 0 0 0;
  max-width: 1150px; /* hoặc giá trị container của bạn */
  margin-left: auto;
  margin-right: auto;
  padding-left: 0; /* hoặc giá trị padding của container */
  padding-right: 0;
}

.breadcrumb-list {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0;
  margin: 0;
  list-style: none;
  flex-wrap: nowrap;
  white-space: nowrap;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
}

.breadcrumb-link {
  color: #222;
  font-weight: 700;
  text-decoration: none;
  transition: color 0.3s ease;
}

.breadcrumb-link.active {
  color: #0283f5;
  font-weight: 700;
}

.breadcrumb-link:hover {
  color: #0283f5;
  text-decoration: underline;
}

.breadcrumb-current {
  color: #0283f5;
  font-weight: 700;
  font-size: 18px;
  text-decoration: none;
}

.breadcrumb-separator {
  margin: 0 8px;
  color: #888;
  font-size: 18px;
}
