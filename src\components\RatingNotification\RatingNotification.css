/* <PERSON><PERSON><PERSON> b<PERSON><PERSON> thông b<PERSON>o hiển thị đúng */
.ant-notification {
  z-index: 9999 !important;
}

.ant-notification-notice {
  padding: 16px !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }

  100% {
    transform: scale(1);
  }
}

.rating-notification-btn {
  animation: pulse 2s infinite;
  padding: 5px 15px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.rating-notification-btn:hover {
  background-color: #40a9ff;
}