/* === <PERSON>h gi<PERSON>a <PERSON> toàn màn hình === */
.register-screen .ant-modal {
  top: 50% !important;
  transform: translateY(-50%) !important;
  margin: 0 auto;
}

.register-screen .ant-modal-content {
  padding: 24px 32px;
  border-radius: 18px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

/* === Hộp đăng ký === */
.register-box {
  width: 100%;
  max-width: 500px;
}

/* === Header === */
.register-header-top {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
}

.brand-logo {
  width: 60px;
  margin-bottom: 8px;
}

.register-header-top h2 {
  font-size: 22px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.register-subtitle {
  font-size: 14px;
  color: #888;
  margin-bottom: 16px;
}

/* === Input form === */
.ant-input,
.ant-picker,
.ant-select-selector {
  border-radius: 8px !important;
  padding: 10px;
  font-size: 14px;
}

.ant-form-item {
  margin-bottom: 16px;
}
.auth-divider {
  text-align: center;
  margin: 16px 0;
  font-size: 12px;
  color: #888;
}

.social-buttons {
  display: flex;
  justify-content: center;
  gap: 16px;
  flex-wrap: wrap;
}

.social-button {
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid #ccc;
  background-color: white;
  font-size: 12px;
  cursor: pointer;
  min-width: 140px;
}

.google {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
}

.auth-footer {
  margin-top: 16px;
  text-align: center;
  font-size: 12px;
  color: #888;
}
