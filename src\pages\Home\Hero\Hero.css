/* ===== HERO SECTION STYLES ===== */
.hero {
  position: relative;
  height: 60vh;
  overflow: hidden;
  margin-top: 0;
}

.hero-slider {
  position: relative;
  height: 60vh !important;
  overflow: hidden;
}

/* Splide container styling - HERO SPECIFIC */
.hero .hero-slider .splide__track {
  height: 60vh !important;
}

.hero .hero-slider .splide__list {
  height: 60vh !important;
}

.hero .hero-slider .splide__slide {
  height: 60vh !important;
}
.hero-inner {
  width: 100vw;
  max-width: 100vw;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Splide slide styling */
/* Hero specific slide styling */
.hero .splide__slide {
  position: relative;
  width: 100%;
  height: 60vh;
}

.hero-slide {
  position: relative;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
}

.hero-slide::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  /* Giảm opacity để ảnh sáng hơn */
  background: linear-gradient(
    to right,
    rgba(0, 0, 0, 0.25),
    rgba(0, 0, 0, 0.08)
  );
  z-index: 1;
}

.hero-content {
  position: relative;
  z-index: 2;
  max-width: 600px;
  padding: 0 var(--spacing-lg, 1.5rem);
  color: var(--text-white, #fff);
}

.hero-content h1 {
  font-size: 42px;
  font-weight: 700;
  margin-bottom: var(--spacing-lg, 1.5rem);
}

.hero-content p {
  font-size: 18px;
  margin-bottom: var(--spacing-xl, 2rem);
}

/* ===== RESPONSIVE STYLES ===== */
@media (max-width: 768px) {
  .hero {
    height: 45vh;
  }

  .hero-content h1 {
    font-size: 32px;
  }

  .hero-content p {
    font-size: 16px;
  }

  .hero .splide__slide {
    height: 45vh;
  }
}

@media (max-width: 576px) {
  .hero {
    height: 35vh;
  }

  .hero-content h1 {
    font-size: 28px;
    margin-bottom: var(--spacing-md, 1rem);
  }

  .hero-content p {
    font-size: 14px;
    margin-bottom: var(--spacing-lg, 1.5rem);
  }

  .hero .splide__slide {
    height: 35vh;
  }
}
.hero-fadeout {
  position: absolute;
  left: 0;
  right: 0;
  bottom: -1px;
  height: 48px; /* hoặc chiều cao bạn muốn */
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0) 0%,
    #f6f8ff 100%
  );
  z-index: 10;
  pointer-events: none;
}
