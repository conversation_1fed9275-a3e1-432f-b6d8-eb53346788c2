/* Container và Section */
.home-services {
  padding: 80px 0;
  background: #e8f4fd;
  text-align: center;
}

.home-services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 32px;
  margin-top: 40px;
}

.home-section-title {
  display: block;
  margin-left: auto;
  margin-right: auto;
  text-align: center;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 24px;
  background: black;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
  letter-spacing: 1px;
  width: fit-content;
}

.home-section-subtitle {
  display: block;
  margin-left: auto;
  margin-right: auto;
  text-align: center;
  font-size: 1rem;
  font-weight: 400;
  margin-bottom: 16px;
  color: var(--text-secondary);
}

/* Card */
.home-service-card {
  background: #fff;
  border: 1px solid #e5e7eb;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease-in-out;
  text-align: left;
  display: flex;
  flex-direction: column;
  gap: 12px;
  color: #1f2937;
}

.home-service-card:hover {
  box-shadow: 0 16px 32px rgba(0, 0, 0, 0.12);
}

/* Header & Icon */
.home-service-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.home-service-icon {
  font-size: 20px;
  color: white;
  padding: 10px;
  border-radius: 10px;
  width: 40px;
  height: 40px;
  text-align: center;
  line-height: 20px;
  background: #00b5f1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.home-service-icon img {
  width: 27px;
  height: 27px;
  stroke: white;
}

/* Title & Text */
.home-service-card h3 {
  font-size: 16px;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
}

.home-service-card p {
  font-size: 14px;
  color: #4b5563;
  margin: 4px 0 0;
  line-height: 1.6;
}

/* List */
.home-service-card ul {
  list-style: none;
  padding: 0;
  margin: 12px 0 16px;
}

.home-service-card ul li {
  font-size: 14px;
  padding-left: 20px;
  position: relative;
  margin-bottom: 8px;
  color: #1f2937;
}

.home-service-card ul li::before {
  content: "\2713";
  color: #22c55e;
  position: absolute;
  left: 0;
  font-size: 14px;
  top: 0;
}

/* Button */
.home-service-button {
  background: #0283f5;
  color: white;
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  font-size: 14px;
  width: 100%;
  text-align: center;
  transition: background 0.3s ease;
}

.home-service-button:hover {
  background: linear-gradient(to top, #0283f5, #2753d0);
  filter: brightness(1.15);
  transform: translateY(-1px);
  box-shadow: rgba(0, 0, 0, 0.35) 0px -50px 36px -28px inset;
}
