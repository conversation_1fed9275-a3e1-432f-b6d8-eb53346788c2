* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.booking-confirmation-container {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, sans-serif;
  background-color: #f5f5f5;
  color: #333;
  line-height: 1.5;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.booking-notification {
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.booking-notification-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.booking-notification-icon {
  width: 20px;
  height: 20px;
  background-color: #ffc107;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.booking-notification-text {
  margin: 0;
  color: #856404;
}

.booking-main-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
}

.booking-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.booking-card-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #2c5aa0;
}

.booking-user-profile {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 20px;
}

.booking-user-avatar {
  width: 48px !important;
  height: 48px !important;
  border-radius: 50%;
  background-color: #ddd;
}

.booking-user-info h3 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 4px;
  margin-top: 0;
}

.booking-user-info p {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.booking-info-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 12px;
  font-size: 14px;
}

.booking-info-label {
  font-weight: 500;
  color: #495057;
  min-width: 120px;
}

.booking-info-value {
  flex: 1;
}

.booking-price-confirmation {
  font-size: 18px;
  font-weight: 600;
  color: #38a169; /* màu xanh như ảnh */
}

.booking-service-name {
  color: #007bff;
  font-weight: 600;
}

.booking-consultant-name {
  color: #2753d0;
  font-weight: 600;
}

/* CSS cho consultant profile section */
.booking-consultant-profile {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.booking-consultant-avatar {
  background-color: #2753d0;
  color: white;
  font-weight: 600;
}

.booking-consultant-info h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #2753d0;
}

.booking-consultant-info p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.booking-consultant-specialization {
  color: #666 !important;
}

/* CSS cho loading và no-token states */
.booking-no-token-message {
  padding: 40px;
  color: #2753d0;
  font-weight: bold;
  font-size: 30px;
  text-align: center;
}

.booking-loading-message {
  padding: 40px;
  color: #2753d0;
  font-weight: bold;
  font-size: 18px;
  text-align: center;
}

/* CSS cho deposit modal */
.deposit-modal-content {
  padding: 16px 0;
}

.deposit-warning-box {
  background-color: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
}

.deposit-warning-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.deposit-warning-icon {
  font-size: 18px;
  margin-right: 8px;
  color: #fa8c16;
}

.deposit-warning-title {
  color: #fa8c16;
}

.deposit-warning-text {
  margin: 0;
  line-height: 1.6;
}

.deposit-price-highlight {
  color: #2753d0;
}

.deposit-details {
  font-size: 14px;
  color: #666;
}

.deposit-details-list {
  padding-left: 20px;
  margin: 0;
}

.deposit-amount-highlight {
  color: #2753d0;
}

.booking-confirm-section {
  margin-top: 32px;
  text-align: center;
}

.booking-confirm-button {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 14px 48px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.booking-confirm-button:hover {
  background-color: #0056b3;
}

.booking-no-data {
  padding: 40px;
  text-align: center;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  color: #666;
  font-size: 16px;
}

.booking-payment-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-top: 20px;
}

.booking-payment-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #2c5aa0;
}

.booking-payment-method {
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.booking-payment-method:hover {
  border-color: #007bff;
  background-color: #f8f9ff;
}

.booking-payment-method.selected {
  border-color: #007bff;
  background-color: #f8f9ff;
}

.booking-payment-method input[type="radio"] {
  accent-color: #007bff;
  margin: 0;
}

.booking-payment-logo {
  width: 40px;
  height: 40px;
  object-fit: contain;
  border-radius: 6px;
  flex-shrink: 0;
}

.booking-payment-info {
  flex: 1;
}

.booking-payment-info label {
  font-weight: 500;
  color: #333;
  cursor: pointer;
  margin: 0;
  display: block;
  font-size: 15px;
}

.booking-payment-info p {
  font-size: 12px;
  color: #666;
  margin: 4px 0 0 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .booking-main-content {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .booking-confirmation-container {
    padding: 10px;
  }

  .booking-user-profile {
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }

  .booking-info-item {
    flex-direction: column;
    gap: 4px;
  }

  .booking-info-label {
    min-width: auto;
  }

  .booking-confirm-button {
    width: 100%;
    padding: 14px 24px;
  }

  .booking-payment-logo {
    width: 36px;
    height: 36px;
  }
}
