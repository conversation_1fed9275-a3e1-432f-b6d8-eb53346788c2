:root {
  --font-family-sans: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
  --color-primary: #4f46e5;      /* <PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>) */
  --color-primary-light: #e0e7ff; /* N<PERSON>n của ngày rụng trứng */
  --color-danger: #dc2626;       /* <PERSON><PERSON><PERSON> cho ngày kinh */
  --color-danger-light: #fee2e2;  /* Nền của ngày kinh */
  --color-text-dark: #1f2937;     /* <PERSON><PERSON> chính */
  --color-text-light: #6b7280;    /* <PERSON><PERSON> ph<PERSON>, ngày trong tuần */
  --color-border: #e5e7eb;        /* Đường viền */
  --color-bg-hover: #f3f4f6;       /* <PERSON><PERSON><PERSON> khi di chuột */
}

.calendar-container {
  font-family: var(--font-family-sans);
  background: white;
  border: 1px solid var(--color-border);
  border-radius: 1rem; 
  padding: 1.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.07), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  max-width: 500px; 
  margin: 2rem auto; 
}
.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.calendar-header span {
  font-size: 1.125rem; 
  font-weight: 600;
  color: var(--color-text-dark);
  text-transform: capitalize;
}

.calendar-header button {
  background: transparent;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--color-text-light);
  transition: background-color 0.2s, color 0.2s;
}

.calendar-header button:hover {
  background-color: var(--color-bg-hover);
  color: var(--color-text-dark);
}
.calendar-header button:first-of-type::before {
  content: '‹';
  font-size: 1.5rem;
}
.calendar-header button:last-of-type::before {
  content: '›';
  font-size: 1.5rem;
}
.days-of-week, .calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
}

.days-of-week div {
  padding-bottom: 0.75rem;
  font-weight: 500;
  color: var(--color-text-light);
  font-size: 0.875rem; 
  text-align: center;
}

.calendar-grid {
  gap: 4px; 
}

.day-cell {
  position: relative;
  border: none;
  background-color: transparent;
  font-family: inherit;
  font-size: 0.875rem; 
  padding: 0;
  cursor: pointer;
  height: 52px;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.day-cell:not(.disabled):hover {
  background-color: var(--color-bg-hover);
}

.day-cell.disabled {
  color: #d1d5db;
  cursor: default;
}

.day-cell.disabled:hover {
  background-color: transparent;
}
.day-cell.today {
  border: 2px solid var(--color-primary);
  font-weight: 700;
  color: var(--color-primary);
}
.day-cell.period-day {
  background-color: var(--color-danger-light);
  color: var(--color-danger);
  font-weight: 600;
}
.day-cell.ovulation-day {
  background-color: var(--color-primary-light);
  color: var(--color-primary);
  font-weight: 700;
  border: 2px solid var(--color-primary);
}
.day-cell.predicted-day {
  background-image: repeating-linear-gradient(
    -45deg,
    transparent,
    transparent 5px,
    #fdecec 5px,
    #fdecec 10px
  );
  color: var(--color-danger);
}
.symptom-dot {
  position: absolute;
  top: 6px;
  right: 6px; 
  width: 6px;
  height: 6px;
  background-color: var(--color-primary);
  border-radius: 50%;
  border: 1px solid white; 
}

.period-label {
  position: absolute;
  bottom: 4px;
  left: 0;
  right: 0;
  font-size: 0.65rem; 
  font-weight: 500;
  text-align: center;
  pointer-events: none;
  color: inherit;
  opacity: 0.8;
}
.bear-icon {
  font-size: 0.7em;
  position: absolute;
  top: -14px;
  right: -10px;
  z-index: 2;
  pointer-events: none;
  background: transparent;
}