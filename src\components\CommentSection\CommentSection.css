/* Comment Section Styles */
.comment-section {
  margin-top: 2rem;
  padding: 1.5rem;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
}

.comment-section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #f0f0f0;
}

.comment-count {
  font-size: 1.1rem;
  font-weight: 600;
  color: #262626;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.comment-count svg {
  color: #1890ff;
}

.login-prompt {
  background: linear-gradient(135deg, #f6f9fc 0%, #e9f3ff 100%);
  border: 1px solid #d6e7ff;
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
  margin-bottom: 1.5rem;
}

.login-prompt p {
  margin: 0 0 0.75rem 0;
  color: #595959;
  font-size: 0.95rem;
}

.login-btn {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  color: white;
  border: none;
  padding: 0.5rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.login-btn:hover {
  background: linear-gradient(135deg, #096dd9 0%, #0050b3 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

/* Comment Form Styles */
.comment-form {
  background: #fafafa;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  transition: all 0.3s ease;
}

.comment-form:focus-within {
  border-color: #1890ff;
  background: #ffffff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

.comment-form-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
}

.comment-form-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #f0f0f0;
}

.comment-form-user {
  display: flex;
  flex-direction: column;
}

.comment-form-username {
  font-weight: 600;
  color: #262626;
  font-size: 0.9rem;
}

.comment-form-prompt {
  color: #8c8c8c;
  font-size: 0.8rem;
}

.comment-input-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.comment-textarea {
  width: 100%;
  min-height: 80px;
  padding: 0.75rem;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  font-family: inherit;
  font-size: 0.9rem;
  line-height: 1.5;
  resize: vertical;
  transition: all 0.3s ease;
  background: #ffffff;
}

.comment-textarea:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

.comment-textarea:disabled {
  background: #f5f5f5;
  cursor: not-allowed;
}

.comment-form-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.char-count {
  font-size: 0.8rem;
  color: #8c8c8c;
}

.char-warning {
  color: #ff4d4f !important;
  font-weight: 500;
}

.comment-form-actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.cancel-btn {
  background: transparent;
  color: #8c8c8c;
  border: 1px solid #d9d9d9;
  padding: 0.4rem 0.8rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.85rem;
  transition: all 0.3s ease;
}

.cancel-btn:hover {
  color: #595959;
  border-color: #b5b5b5;
}

.submit-btn {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  font-size: 0.85rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.submit-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #096dd9 0%, #0050b3 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.submit-btn:disabled {
  background: #d9d9d9;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Comment List Styles */
.comment-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.comment-item {
  background: #ffffff;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 1rem;
  transition: all 0.3s ease;
}

.comment-item:hover {
  border-color: #e6f7ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.08);
}

.comment-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
}

.comment-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #f0f0f0;
}

.comment-meta {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.2rem;
}

.comment-author {
  font-weight: 600;
  color: #262626;
  font-size: 0.9rem;
}

.comment-time {
  color: #8c8c8c;
  font-size: 0.8rem;
}

.comment-edited {
  color: #8c8c8c;
  font-size: 0.75rem;
  font-style: italic;
}

.comment-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.delete-btn {
  background: transparent;
  border: none;
  color: #8c8c8c;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-btn:hover {
  color: #ff4d4f;
  background: #fff2f0;
}

.delete-btn:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.confirm-delete {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.8rem;
}

.confirm-text {
  color: #8c8c8c;
  white-space: nowrap;
}

.confirm-yes {
  background: #ff4d4f;
  color: white;
  border: none;
  padding: 0.2rem 0.5rem;
  border-radius: 3px;
  cursor: pointer;
  font-size: 0.75rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  min-width: 32px;
  justify-content: center;
}

.confirm-yes:hover:not(:disabled) {
  background: #d9363e;
}

.confirm-no {
  background: #f5f5f5;
  color: #595959;
  border: 1px solid #d9d9d9;
  padding: 0.2rem 0.5rem;
  border-radius: 3px;
  cursor: pointer;
  font-size: 0.75rem;
  transition: all 0.3s ease;
}

.confirm-no:hover {
  background: #e6e6e6;
}

.comment-content {
  margin-left: 0.75rem;
}

.comment-content p {
  margin: 0;
  color: #262626;
  line-height: 1.6;
  font-size: 0.9rem;
  word-wrap: break-word;
  white-space: pre-wrap;
}

/* Loading States */
.loading-spinner {
  width: 12px;
  height: 12px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  display: inline-block;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.comments-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  color: #8c8c8c;
  font-size: 0.9rem;
  gap: 0.5rem;
}

.comments-loading .loading-spinner {
  width: 16px;
  height: 16px;
  border-width: 2px;
}

.comments-empty {
  text-align: center;
  padding: 2rem;
  color: #8c8c8c;
  font-size: 0.9rem;
}

.comments-empty svg {
  margin-bottom: 0.5rem;
  opacity: 0.5;
}

.comments-error {
  background: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 6px;
  padding: 1rem;
  color: #cf1322;
  font-size: 0.9rem;
  text-align: center;
}

/* Responsive Design */
@media (max-width: 768px) {
  .comment-section {
    padding: 1rem;
    margin-top: 1rem;
  }
  
  .comment-form {
    padding: 0.75rem;
  }
  
  .comment-item {
    padding: 0.75rem;
  }
  
  .comment-avatar,
  .comment-form-avatar {
    width: 28px;
    height: 28px;
  }
  
  .comment-textarea {
    min-height: 60px;
    font-size: 0.85rem;
  }
  
  .comment-form-actions {
    flex-direction: column;
    gap: 0.5rem;
    align-items: stretch;
  }
  
  .submit-btn,
  .cancel-btn {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .comment-section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .comment-header {
    flex-wrap: wrap;
  }
  
  .comment-meta {
    min-width: 0;
  }
  
  .comment-author {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
