.profile-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.profile-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0;
}

.profile-header h2 {
  margin: 0;
  color: #1890ff;
  font-weight: 600;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 20px;
}

/* Avatar upload styling */
.avatar-container {
  position: relative;
  display: inline-block;
  cursor: pointer;
}

.profile-avatar {
  margin-bottom: 16px;
  border: 3px solid #f0f0f0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.profile-avatar:hover {
  opacity: 0.8;
}

.avatar-upload-button {
  position: absolute;
  bottom: 20px;
  right: 0;
  z-index: 10;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.avatar-uploader {
  margin-top: 8px;
}

.avatar-uploader .ant-upload {
  border: none !important;
  background: transparent !important;
}

.user-basic-info {
  margin-top: 16px;
}

.user-basic-info h3 {
  margin: 8px 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: #262626;
}

.user-basic-info p {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

/* Form styling */
.ant-form-item-label>label {
  font-weight: 600;
  color: #262626;
}

.ant-input-affix-wrapper {
  border-radius: 6px;
}

.ant-input-affix-wrapper:focus,
.ant-input-affix-wrapper-focused {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.ant-picker {
  border-radius: 6px;
}

.ant-picker:focus,
.ant-picker-focused {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* Card styling */
.ant-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid #f0f0f0;
}

.ant-card-head {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 24px;
}

.ant-card-body {
  padding: 24px;
}

/* Button styling */
.ant-btn-primary {
  background: #1890ff;
  border-color: #1890ff;
  border-radius: 6px;
  font-weight: 500;
}

.ant-btn-primary:hover {
  background: #40a9ff;
  border-color: #40a9ff;
}

.ant-btn {
  border-radius: 6px;
  font-weight: 500;
}

/* Responsive */
@media (max-width: 768px) {
  .profile-container {
    padding: 16px;
  }

  .profile-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .avatar-section {
    padding: 16px 0;
  }

  .profile-avatar {
    width: 100px !important;
    height: 100px !important;
  }

  .form-actions {
    flex-direction: column;
  }

  .form-actions .ant-btn {
    width: 100%;
  }
}

@media (max-width: 576px) {
  .profile-header h2 {
    font-size: 18px;
  }

  .profile-avatar {
    width: 80px !important;
    height: 80px !important;
  }

  .user-basic-info h3 {
    font-size: 16px;
  }
}

/* Loading state */
.ant-card-loading .ant-card-body {
  padding: 40px 24px;
}

/* Upload button styling */
.avatar-uploader .ant-btn {
  margin-top: 8px;
  border: 1px dashed #d9d9d9;
  background: #fafafa;
}

.avatar-uploader .ant-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}

/* Form item spacing */
.ant-form-item {
  margin-bottom: 20px;
}

.ant-form-item:last-child {
  margin-bottom: 0;
}

/* Input focus effects */
.ant-input:focus,
.ant-input-focused {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.ant-input[disabled] {
  background-color: #f5f5f5;
  color: #8c8c8c;
}

/* Date picker styling */
.ant-picker-input>input {
  font-size: 14px;
}

/* Text area styling */
.ant-input {
  border-radius: 6px;
}

.ant-input:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* Preview modal */
.avatar-preview-modal .ant-modal-body {
  text-align: center;
  padding: 24px;
}

.avatar-preview-modal img {
  max-width: 100%;
  max-height: 500px;
}

.certificates-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.certificate-item {
  position: relative;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  margin-bottom: 16px;
  background: #fafafa;
  transition: all 0.3s ease;
  min-height: 140px;
  /* Đảm bảo chiều cao tối thiểu */
}

.certificate-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-color: #d9d9d9;
}

.certificate-item h3 {
  margin: 8px 0 8px 0;
  color: #1890ff;
  font-weight: 600;
  padding-right: 40px;
}

.certificate-item p {
  margin: 4px 0;
  color: #666;
  font-size: 14px;
}

.certificate-image-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-right: 40px;
  /* Tạo khoảng cách với nút 3 chấm */
}

.certificate-image {
  width: 100%;
  max-height: 120px;
  object-fit: cover;
  border-radius: 8px;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.certificate-image:hover {
  transform: scale(1.05);
}

.empty-certificates {
  text-align: center;
  padding: 40px 20px;
  color: #999;
}

.empty-certificates p {
  font-size: 16px;
  margin: 0;
}

/* Responsive */
@media (max-width: 768px) {
  .certificate-item h3 {
    font-size: 16px;
    padding-right: 35px;
  }

  .certificate-item {
    padding: 12px;
    min-height: auto;
  }

  .certificate-image-container {
    padding-right: 35px;
    margin-top: 12px;
  }
}