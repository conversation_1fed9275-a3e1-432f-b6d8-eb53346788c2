/* ===== LOGO STYLES ===== */
.logo {
  display: flex;
  align-items: center;
}

.logo-link {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: var(--primary-color);
  transition: all var(--transition-fast);
}

.logo-link:hover {
  opacity: 0.8;
}

/* ===== LOGO ICON ===== */
.logo-icon {
  font-size: 24px;
  margin-right: var(--spacing-xs);
  color: var(--primary-color);
  font-weight: bold;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* ===== LOGO TEXT ===== */
.logo-text {
  display: flex;
  flex-direction: column;
}

.logo-main {
  font-size: 16px;
  font-weight: 700;
  line-height: 1.2;
  color: #ff5a7d;
}

/* ===== RESPONSIVE STYLES ===== */
@media (max-width: 768px) {
  .logo-icon {
    font-size: 22px;
  }

  .logo-main {
    font-size: 15px;
  }
}

@media (max-width: 576px) {
  .logo-icon {
    font-size: 20px;
  }

  .logo-main {
    font-size: 14px;
  }
}
