.cycle-tracker-container {
  max-width: 1000px;
  margin: 0 auto;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: #333;
  background: #f4f8fb;
  border-radius: 1.5rem;
  box-shadow: 0 8px 32px rgba(0,0,0,0.10);
  padding: 2.5rem 2rem 2rem 2rem;
}

.tracker-header {
  text-align: center;
  margin-bottom: 2rem;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 1.5rem;
}

.tracker-header h1 {
  font-size: 2.5rem;
  color: #4f46e5;
  margin-bottom: 0.5rem;
  letter-spacing: 0.5px;
}

.tracker-header p {
  color: #6b7280;
  font-size: 1.1rem;
}

.tracker-notifications {
  margin-bottom: 2rem;
}

.notification-item {
  padding: 1rem 1.5rem;
  border-radius: 10px;
  font-size: 1rem;
  margin-bottom: 1rem;
  border-left: 5px solid;
  box-shadow: 0 2px 8px rgba(79,70,229,0.06);
}

.notification-item.warning {
  background-color: #fffbe6;
  border-color: #f59e0b;
  color: #b45309;
}

.notification-item.info {
  background-color: #eff6ff;
  border-color: #3b82f6;
  color: #1e40af;
}

.tracker-body {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2.5rem;
  align-items: flex-start;
}

.calendar-section {
  background: #fff;
  border-radius: 1.2rem;
  box-shadow: 0 2px 8px rgba(79,70,229,0.06);
  padding: 1.5rem 0.5rem;
}

.info-section {
  background: #f9fafb;
  border-radius: 1.2rem;
  box-shadow: 0 2px 8px rgba(79,70,229,0.06);
  padding: 1.5rem 1.2rem;
}

.info-section h2 {
  font-size: 1.5rem;
  color: #4f46e5;
  margin-bottom: 1rem;
}

.info-card {
  background: #fff;
  padding: 1.2rem 1rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
}

.info-card p {
  margin: 0 0 0.75rem 0;
  font-size: 1rem;
}

.info-card p:last-child {
  margin-bottom: 0;
}

.legend {
  margin-top: 1.5rem;
}

.legend h3 {
  font-size: 1.1rem;
  color: #6366f1;
  margin-bottom: 0.7rem;
}

.legend ul {
  list-style: none;
  padding: 0;
}

.legend li {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
  font-size: 0.98rem;
}

.legend-color {
  display: inline-block;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  margin-right: 10px;
  border: 2px solid #e5e7eb;
}

.legend-color.period { background-color: #fee2e2; border-color: #dc2626; }
.legend-color.predicted { background: repeating-linear-gradient(-45deg, #fff, #fff 6px, #fdecec 6px, #fdecec 12px); border-color: #fecaca; }
.legend-color.ovulation { background-color: #e0e7ff; border-color: #6366f1; }
.legend-color.today { border: 2px solid #4f46e5; background-color: #fff; }
.legend-dot {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #4f46e5;
  margin-right: 10px;
  border: 1.5px solid #fff;
  box-shadow: 0 1px 2px rgba(79,70,229,0.10);
}
.period-history-table {
  width: 100%;
  border-collapse: collapse;
}
.period-history-table th, .period-history-table td {
  border: 1px solid #e5e7eb;
  padding: 8px 12px;
  text-align: center;
}
.period-history-table th {
  background: #f3f4f6;
  color: #4f46e5;
  font-weight: 700;
}

.period-history-table tr:nth-child(even) {
  background: #f8fafc;
}

.period-history-table tr {
  border-bottom: 1px solid #e5e7eb;
}

.period-history-table td {
  color: #374151;
  vertical-align: top;
}

.period-history-table tr:last-child td {
  border-bottom: none;
}

/* Đẹp hơn trên mobile */
@media (max-width: 600px) {
  .period-history-table th,
  .period-history-table td {
    padding: 8px 6px;
    font-size: 0.98rem;
  }
}
.modal-content.guide-modal {
  max-height: 70vh;      /* Giới hạn chiều cao modal */
  overflow-y: auto;      /* Hiện thanh lăn khi nội dung dài */
  padding-right: 18px;   /* Để nội dung không bị che bởi thanh lăn */
}

@media (max-width: 1100px) {
  .cycle-tracker-container {
    padding: 1.2rem 0.2rem;
    border-radius: 1rem;
    max-width: 100vw;
  }
  .tracker-body {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  .calendar-section, .info-section {
    padding: 1rem 0.2rem;
    border-radius: 0.7rem;
  }

}