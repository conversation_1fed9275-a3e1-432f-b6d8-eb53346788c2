.doctor-list-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}
.doctor-card {
  display: flex;
  align-items: center;
  background-color: #ffffff;
  border-radius: 12px;
  border: 1px solid #e9e9e9;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: box-shadow 0.2s ease;
}
.doctor-card:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}
.doctor-image {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  margin-right: 24px;
  flex-shrink: 0;
}
.doctor-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.doctor-name {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: #222;
}

.doctor-specialty {
  font-size: 1rem;
  color: #007bff;
  font-weight: 500;
  margin-bottom: 12px;
}

.doctor-experience,
.doctor-workplace {
  font-size: 0.9rem;
  color: #666;
  margin: 2px 0;
}

.book-appointment-btn {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  margin-top: 16px;
  align-self: flex-start;
  transition: background-color 0.2s;
}
.book-appointment-btn:hover {
  background-color: #0056b3;
}
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px; /* Tăng khoảng cách giữa các nút một chút */
  margin-top: 30px;
}

/* Kiểu chung cho tất cả các nút (bao gồm cả nút mũi tên và nút số) */
.pagination button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 42px; /* Đặt kích thước cố định để các nút vuông vắn */
  height: 42px;
  border: 1px solid #e0e7ff; /* Viền xanh nhạt, mỏng */
  background-color: #ffffff;
  color: #007bff; /* Màu chữ xanh cho nút thường */
  font-size: 1rem;
  font-weight: bold;
  border-radius: 12px; /* Bo góc mềm mại hơn */
  cursor: pointer;
  transition: all 0.2s ease;
}

/* Hiệu ứng khi di chuột vào nút chưa active */
.pagination button:not(.active):not(:disabled):hover {
  background-color: #f0f8ff; /* Nền xanh rất nhạt khi hover */
  border-color: #b3d7ff;
}

/* Kiểu cho nút đang được chọn (active) */
.pagination button.active {
  background-color: #007bff;
  color: #ffffff;
  border-color: #007bff;
  box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3); /* Đổ bóng nhẹ */
  transform: translateY(-2px); /* Hơi nâng nút lên một chút */
}

/* Kiểu cho nút bị vô hiệu hóa (disabled) */
.pagination button:disabled {
  background-color: #f8f9fa;
  color: #ced4da;
  border-color: #e9ecef;
  cursor: not-allowed;
}

/* CSS cho API consultants list */
.api-consultants-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.api-consultant-card {
  background: #f9f9f9;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.api-consultant-card h4 {
  margin: 0 0 8px 0;
  color: #2753d0;
  font-size: 16px;
}

.api-consultant-card p {
  margin: 4px 0;
  font-size: 14px;
  color: #666;
}

.api-consultant-card strong {
  color: #333;
}
