import React from "react";
import "./Loading.css";

function Loading() {
  return (
    <div className="loader">
      <svg className="absolute" width="0" height="0">
        <defs>
          <linearGradient
            id="b"
            x1="0"
            y1="62"
            x2="0"
            y2="2"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#973BED"></stop>
            <stop offset="1" stopColor="#007CFF"></stop>
          </linearGradient>
          <linearGradient
            id="c"
            x1="0"
            y1="64"
            x2="0"
            y2="0"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#FFC800"></stop>
            <stop offset="1" stopColor="#F0F"></stop>
            <animateTransform
              attributeName="gradientTransform"
              type="rotate"
              values="0 32 32;-270 32 32;-540 32 32;-810 32 32;-1080 32 32"
              dur="8s"
              keyTimes="0;0.125;0.25;0.375;0.5;0.625;0.75;0.875;1"
              repeatCount="indefinite"
            ></animateTransform>
          </linearGradient>
          <linearGradient
            id="d"
            x1="0"
            y1="62"
            x2="0"
            y2="2"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#00E0ED"></stop>
            <stop offset="1" stopColor="#00DA72"></stop>
          </linearGradient>
        </defs>
      </svg>

      <svg viewBox="0 0 44 54" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          pathLength="420"
          d="M24.691 19.223c-0.351-0.687-0.806-1.27-1.351-1.752l-0.006-0.005c-0.621-0.5-1.337-0.917-2.112-1.216l-0.054-0.018c-0.619-0.274-1.389-0.552-2.181-0.783l-0.14-0.035c-0.633-0.172-1.506-0.396-2.632-0.678q-1.318-0.313-2.164-0.564c-0.734-0.224-1.336-0.452-1.92-0.714l0.104 0.042c-0.542-0.219-1.010-0.501-1.425-0.845l0.009 0.007c-0.335-0.308-0.613-0.673-0.821-1.079l-0.010-0.022c-0.206-0.396-0.327-0.866-0.327-1.363 0-0.021 0-0.042 0.001-0.063l-0 0.003c-0.001-0.031-0.002-0.068-0.002-0.105 0-1.115 0.567-2.098 1.429-2.676l0.012-0.007c1.15-0.71 2.543-1.131 4.035-1.131 0.168 0 0.335 0.005 0.5 0.016l-0.023-0.001c0.124-0.006 0.27-0.010 0.416-0.010 1.632 0 3.158 0.449 4.464 1.229l-0.040-0.022c1.010 0.655 1.668 1.777 1.668 3.052 0 0.104-0.004 0.206-0.013 0.308l0.001-0.013c0.001 0.442 0.359 0.8 0.801 0.8h0.928c0.442-0 0.8-0.358 0.801-0.8v-0.224c0.002-0.062 0.004-0.134 0.004-0.207 0-2.043-1.039-3.843-2.617-4.9l-0.021-0.013c-1.68-1.089-3.735-1.737-5.94-1.737-0.158 0-0.316 0.003-0.473 0.010l0.022-0.001c-0.16-0.009-0.348-0.014-0.536-0.014-2.085 0-4.024 0.625-5.639 1.698l0.038-0.024c-1.446 1.032-2.378 2.704-2.378 4.593 0 0.063 0.001 0.126 0.003 0.188l-0-0.009c-0 0.020-0 0.044-0 0.068 0 0.906 0.215 1.762 0.598 2.519l-0.015-0.032c0.353 0.709 0.811 1.312 1.362 1.814l0.005 0.004c0.597 0.505 1.285 0.928 2.032 1.238l0.052 0.019c0.575 0.266 1.291 0.538 2.027 0.766l0.133 0.035c0.597 0.175 1.363 0.37 2.285 0.584 0.932 0.233 1.617 0.408 2.051 0.524q0.64 0.173 1.726 0.518c0.554 0.158 1.037 0.367 1.485 0.629l-0.030-0.016c0.399 0.264 0.746 0.532 1.072 0.822l-0.009-0.008c0.279 0.231 0.498 0.526 0.635 0.862l0.005 0.015c0.129 0.348 0.204 0.749 0.204 1.168 0 0.018-0 0.037-0 0.055l0-0.003c0.001 0.029 0.001 0.064 0.001 0.098 0 1.181-0.621 2.218-1.554 2.8l-0.014 0.008c-1.25 0.737-2.754 1.173-4.36 1.173-0.178 0-0.354-0.005-0.529-0.016l0.024 0.001c-0.121 0.006-0.264 0.009-0.407 0.009-1.676 0-3.251-0.435-4.618-1.197l0.048 0.025c-1.12-0.652-1.888-1.801-2.003-3.137l-0.001-0.015c-0.048-0.401-0.386-0.708-0.795-0.708-0.067 0-0.132 0.008-0.194 0.024l0.005-0.001-0.928 0.225c-0.353 0.087-0.611 0.401-0.611 0.776 0 0.036 0.002 0.072 0.007 0.107l-0-0.004c0.23 2.030 1.374 3.753 3.006 4.765l0.027 0.016c1.707 1.020 3.764 1.623 5.962 1.623 0.165 0 0.329-0.003 0.493-0.010l-0.023 0.001c0.176 0.010 0.382 0.016 0.589 0.016 2.193 0 4.239-0.632 5.965-1.724l-0.046 0.027c1.527-1.033 2.518-2.759 2.518-4.716 0-0.057-0.001-0.114-0.003-0.171l0 0.008c0-0.021 0-0.046 0-0.070 0-0.887-0.218-1.722-0.602-2.457l0.014 0.029z"
          className="dash gradient-c"
          strokeWidth="5"
          strokeLinecap="round"
          strokeLinejoin="round"
        ></path>
      </svg>

      <svg viewBox="0 0 44 54" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          pathLength="360"
          d="M25.264 3.993h-0.928c-0.442 0-0.8 0.358-0.801 0.8v9.952h-15.038v-9.952c-0-0.442-0.358-0.8-0.8-0.8h-0.928c-0.442 0-0.8 0.358-0.8 0.8v22.4c0 0.442 0.358 0.8 0.8 0.801h0.928c0.442-0.001 0.8-0.359 0.8-0.801v0-9.952h15.038v9.952c0.001 0.442 0.359 0.8 0.801 0.801h0.928c0.442-0.001 0.8-0.359 0.801-0.801v-22.4c-0.001-0.442-0.359-0.8-0.801-0.8h-0z"
          className="dash gradient-d"
          strokeWidth="4"
          strokeLinecap="round"
          strokeLinejoin="round"
        ></path>
      </svg>

      <svg viewBox="0 0 44 54" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          pathLength="360"
          d="M24.672 6.521c0.442-0 0.8-0.358 0.801-0.8v-0.929c-0.001-0.442-0.359-0.8-0.801-0.8h-15.104c-0.442 0-0.8 0.358-0.8 0.8v22.4c0 0.442 0.358 0.8 0.8 0.801h15.104c0.442-0.001 0.8-0.359 0.801-0.801v-0.928c-0.001-0.442-0.359-0.8-0.801-0.801h-13.376v-8.224h10.911c0.442-0 0.8-0.358 0.801-0.8v-0.928c-0.001-0.442-0.359-0.8-0.801-0.8h-10.911v-8.192z"
          className="dash gradient-b"
          strokeWidth="4"
          strokeLinecap="round"
          strokeLinejoin="round"
        ></path>
      </svg>
      <svg viewBox="0 0 44 54" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          pathLength="360"
          d="M28.34 26.854l-10.527-22.4c-0.131-0.274-0.405-0.46-0.724-0.46-0 0-0 0-0 0h-1.088c-0 0-0 0-0 0-0.318 0-0.593 0.186-0.721 0.455l-0.002 0.005-10.529 22.4c-0.048 0.1-0.077 0.217-0.077 0.341 0 0.441 0.358 0.799 0.799 0.799 0 0 0.001 0 0.001 0h0.992c0.319-0 0.594-0.186 0.723-0.456l0.002-0.005 2.44-5.203h13.801l2.471 5.207c0.131 0.272 0.405 0.457 0.723 0.457h0.992c0 0 0 0 0 0 0.442 0 0.8-0.358 0.8-0.8 0-0.124-0.028-0.241-0.078-0.345l0.002 0.005zM10.798 19.801l5.744-12.17 5.718 12.17z"
          className="dash gradient-b"
          strokeWidth="4"
          strokeLinecap="round"
          strokeLinejoin="round"
        ></path>
      </svg>
      <svg viewBox="0 0 44 54" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          pathLength="360"
          d="M25.568 3.993h-18.080c-0.442 0-0.8 0.358-0.8 0.8v0.929c0 0.442 0.358 0.8 0.8 0.8h7.776v20.672c0 0.442 0.358 0.8 0.8 0.801h0.928c0.442-0.001 0.8-0.359 0.8-0.801v0-20.672h7.776c0.442-0 0.8-0.358 0.801-0.8v-0.929c-0.001-0.442-0.359-0.8-0.801-0.8v0z"
          className="dash gradient-b"
          strokeWidth="4"
          strokeLinecap="round"
          strokeLinejoin="round"
        ></path>
      </svg>
      <svg viewBox="0 0 44 54" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          pathLength="360"
          d="M25.264 3.993h-0.928c-0.442 0-0.8 0.358-0.801 0.8v9.952h-15.038v-9.952c-0-0.442-0.358-0.8-0.8-0.8h-0.928c-0.442 0-0.8 0.358-0.8 0.8v22.4c0 0.442 0.358 0.8 0.8 0.801h0.928c0.442-0.001 0.8-0.359 0.8-0.801v0-9.952h15.038v9.952c0.001 0.442 0.359 0.8 0.801 0.801h0.928c0.442-0.001 0.8-0.359 0.801-0.801v-22.4c-0.001-0.442-0.359-0.8-0.801-0.8h-0z"
          className="dash gradient-b"
          strokeWidth="4"
          strokeLinecap="round"
          strokeLinejoin="round"
        ></path>
      </svg>
      <div className="w-2"></div>
    </div>
  );
}

export default Loading;
