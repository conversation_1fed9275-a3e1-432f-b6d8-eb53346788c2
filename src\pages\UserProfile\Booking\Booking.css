/* Booking.css */

/* Container chính */
.booking-tab-wrapper-profile {
  width: 100%;
}

/* Title */
.booking-title-profile {
  margin: 0 0 32px 0;
  font-size: 28px;
  font-weight: 700;
  color: #1e293b;
  border-bottom: 2px solid #e2e8f0;
  padding-bottom: 16px;
}

/* Tab navigation */
.booking-tabs-profile {
  display: flex;
  gap: 4px;
  margin-bottom: 32px;
  background: #f8fafc;
  padding: 4px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  overflow-x: auto;
}

.tab-button-profile {
  flex: 1;
  min-width: 140px;
  padding: 12px 16px;
  border: none;
  background: transparent;
  color: #64748b;
  font-weight: 500;
  font-size: 14px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.tab-button-profile:hover {
  background: #e2e8f0;
  color: #334155;
}

.tab-button-profile.active {
  background: #3b82f6;
  color: white;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

/* Tab content */
.booking-tab-content-profile {
  min-height: 400px;
}

/* Appointment card */
.booking-card-profile {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin-bottom: 12px;
  transition: all 0.2s ease;
}

.booking-card-profile h2 {
  margin: 0 0 10px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  display: flex;
  align-items: center;
  gap: 4px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f1f5f9;
}

.booking-card-profile h2::before {
  /* content: ""; */
  font-size: 14px;
}

/* Booking info */
.booking-info-profile {
  display: grid;
  gap: 6px;
}

.booking-info-profile p {
  margin: 0;
  display: flex;
  align-items: flex-start;
  gap: 6px;
  padding: 4px 0;
  border-bottom: 1px solid #f8fafc;
  font-size: 14px;
  line-height: 1.4;
}

.booking-info-profile p:last-child {
  border-bottom: none;
}

.booking-info-profile strong {
  color: #374151;
  font-weight: 600;
  min-width: 80px;
  flex-shrink: 0;
  font-size: 15px;
}

/* Status styling */
.status {
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  display: inline-block;
  border: 1px solid;
}

.status.pending {
  background: #fef3c7;
  color: #92400e;
  border-color: #f59e0b;
}

.status.confirmed {
  background: #d1fae5;
  color: #065f46;
  border-color: #10b981;
}

.status.checked {
  background: #dbeafe;
  color: #1e40af;
  border-color: #3b82f6;
}

.status.completed {
  background: #e0e7ff;
  color: #3730a3;
  border-color: #6366f1;
}

.status.canceled {
  background: #fee2e2;
  color: #991b1b;
  border-color: #ef4444;
}

.status.cancelled {
  background: #fee2e2;
  color: #991b1b;
  border-color: #ef4444;
}

/* Loading state */
.booking-loading-profile {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  font-size: 16px;
  color: #64748b;
  background: #f8fafc;
  border-radius: 12px;
  border: 2px dashed #cbd5e1;
}

.booking-loading-profile::before {
  content: "⏳";
  margin-right: 8px;
  font-size: 20px;
  animation: pulse 2s infinite;
}

/* Empty state */
.booking-empty-profile {
  text-align: center;
  padding: 48px 24px;
  background: #f8fafc;
  border-radius: 12px;
  border: 2px dashed #cbd5e1;
}

.booking-empty-profile h3 {
  margin: 0 0 12px 0;
  font-size: 20px;
  font-weight: 600;
  color: #64748b;
}

.booking-empty-profile p {
  margin: 0 0 24px 0;
  color: #94a3b8;
  font-size: 15px;
  line-height: 1.6;
}

.booking-empty-profile button {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.booking-empty-profile button:hover {
  background: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

.booking-empty-profile button:active {
  transform: translateY(0);
}

/* Animations */
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.booking-card-profile,
.booking-empty-profile {
  animation: fadeIn 0.3s ease-out;
}

/* Responsive design */

/* Hover effects đã được tích hợp vào main class */

/* Print styles */
/* Button styling - unified */
.detail-button-profile,
.cancel-button-profile,
.online-consultation-button-profile,
.result-button-profile,
.rate-service-btn {
  margin: 0;
  padding: 8px 16px;
  font-size: 12px;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-right: 8px;
  min-width: 100px;
  font-weight: 500;
  text-align: center;
  line-height: 1.2;
  text-decoration: none;
  display: inline-block;
  box-sizing: border-box;
}

.detail-button-profile {
  background-color: #2753d0;
  box-shadow: 0 2px 4px rgba(39, 83, 208, 0.2);
}

.detail-button-profile:hover {
  background-color: #086ce4;
  transform: translateY(-1px);
}

.cancel-button-profile {
  background-color: #ff4d4f;
  box-shadow: 0 2px 4px rgba(255, 77, 79, 0.2);
}

.cancel-button-profile:hover {
  background-color: #ff7875;
  transform: translateY(-1px);
}

.online-consultation-button-profile {
  background-color: #1890ff;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
}

.online-consultation-button-profile:hover {
  background-color: #40a9ff;
  transform: translateY(-1px);
}

.result-button-profile {
  background-color: #52c41a;
  box-shadow: 0 2px 4px rgba(82, 196, 26, 0.2);
}

.result-button-profile:hover {
  background-color: #73d13d;
  transform: translateY(-1px);
}

.rate-service-btn {
  background-color: #52c41a;
  box-shadow: 0 2px 4px rgba(82, 196, 26, 0.2);
}

.rate-service-btn:hover {
  background-color: #73d13d;
  transform: translateY(-1px);
}

.rate-service-btn.rated {
  background-color: #faad14;
  box-shadow: 0 2px 4px rgba(250, 173, 20, 0.2);
}

.rate-service-btn.rated:hover {
  background-color: #ffc53d;
  transform: translateY(-1px);
}

.online-consultation-button-profile.disabled {
  background-color: #d9d9d9;
  color: #999;
  cursor: not-allowed;
}

.online-consultation-button-profile.disabled:hover {
  background-color: #d9d9d9;
  transform: none;
}

/* Modal styling */
.appointment-detail-modal .ant-modal-header {
  border-bottom: none;
}

.appointment-detail-modal .ant-modal-title {
  color: #2753d0;
  font-weight: 600;
  font-size: 24px;
}

.appointment-detail-modal .ant-modal-close {
  color: white;
}

.appointment-detail-modal .ant-modal-close:hover {
  color: #f0f0f0;
}

.appointment-detail-content {
  padding: 0;
}

.detail-sections {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.detail-section {
  background: #f8f9fa;
  border-radius: 0px;
  padding: 16px;
  border-left: 4px solid #2753d0;
}

.section-title {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #2753d0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
  padding: 8px 0;
  border-bottom: 1px solid #e8e8e8;
}

.detail-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.detail-label {
  font-weight: 500;
  color: #666;
  min-width: 140px;
  flex-shrink: 0;
}

.detail-value {
  color: #333;
  text-align: right;
  word-break: break-word;
}

.result-link {
  color: #1890ff !important;
  cursor: pointer;
  text-decoration: underline;
  transition: color 0.3s ease;
}

.result-link:hover {
  color: #40a9ff !important;
  text-decoration: underline;
}

.detail-value.price {
  font-weight: 600;
  color: #2753d0;
}

.detail-value.status-pending {
  color: #faad14;
  font-weight: 500;
}

.detail-value.status-confirmed {
  color: #52c41a;
  font-weight: 500;
}

.detail-value.status-completed {
  color: #1890ff;
  font-weight: 500;
}

.detail-value.status-canceled {
  color: #ff4d4f;
  font-weight: 500;
}

/* Appointment Actions Container */
.appointment-actions {
  display: flex;
  gap: 10px;
  margin-top: 10px;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-start;
}

/* Modal chi tiết lịch hẹn - Simple */
.appointment-detail-modal {
  text-align: center;
}

.appointment-detail-modal .ant-modal {
  text-align: left;
  display: inline-block;
  vertical-align: middle;
}

.appointment-detail-modal .ant-modal-content {
  border-radius: 8px;
}

.appointment-detail-modal .ant-modal-header {
  border-bottom: none;
}

.appointment-detail-modal .ant-modal-title {
  font-size: 20px;
  font-weight: 600;
  color: #2753d0;

  .appointment-detail-modal .ant-modal-close {
    color: white;
  }

  .appointment-detail-modal .ant-modal-close:hover {
    color: #f0f0f0;
  }

  .appointment-detail-content {
    max-height: 70vh;
    overflow-y: auto;
    padding: 0;
  }

  .detail-section {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
  }

  .detail-section:last-child {
    border-bottom: none;
  }

  .detail-section h3 {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: #2753d0;
    padding-bottom: 8px;
  }

  .detail-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
    padding: 4px 0;
  }

  .detail-item:last-child {
    margin-bottom: 0;
  }

  .detail-label {
    font-weight: 500;
    color: #666;
    min-width: 140px;
    flex-shrink: 0;
  }

  .detail-value {
    color: #333;
    text-align: right;
    flex: 1;
    word-break: break-word;
  }

  .detail-value.status {
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.2px;
    display: inline-block;
    border: 1px solid;
    width: fit-content;
    max-width: 120px;
    align-items: center;
  }

  .detail-value.status.confirmed {
    background: #d1fae5;
    color: #065f46;
    border-color: #10b981;
    text-align: center;
  }

  .detail-value.status.pending {
    background: #fef3c7;
    color: #92400e;
    border-color: #f59e0b;
  }

  .detail-value.status.checked {
    background: #dbeafe;
    color: #1e40af;
    border-color: #3b82f6;
  }

  .detail-value.status.completed {
    background: #e0e7ff;
    color: #3730a3;
    border-color: #6366f1;
  }

  .detail-value.status.canceled {
    background: #fee2e2;
    color: #991b1b;
    border-color: #ef4444;
  }

  .detail-value a {
    color: #2753d0;
    text-decoration: none;
    font-weight: 500;
  }

  .detail-value a:hover {
    text-decoration: underline;
  }

  @keyframes zoomBlink {
    0% {
      background-color: #1890ff;
      box-shadow: 0 0 5px rgba(24, 144, 255, 0.5);
      transform: scale(1);
    }

    100% {
      background-color: #40a9ff;
      box-shadow: 0 0 15px rgba(24, 144, 255, 0.8);
      transform: scale(1.05);
    }
  }

  /* Styles cho nút đánh giá */
  .appointment-actions {
    display: flex;
    gap: 10px;
    margin-top: 10px;
  }

  .view-detail-btn {
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    border: none;
    transition: all 0.3s ease;
    background-color: #1890ff;
    color: white;
  }

  .view-detail-btn:hover {
    background-color: #40a9ff;
  }

  /* Styles cho status badge */
  .status-badge {
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 12px;
    font-weight: 500;
    margin-left: 5px;
  }

  .status-badge.completed {
    background-color: #52c41a;
    color: white;
  }

  .status-badge.pending {
    background-color: #faad14;
    color: white;
  }

  .status-badge.cancelled {
    background-color: #ff4d4f;
    color: white;
  }

  .status-badge.confirmed {
    background-color: #1890ff;
    color: white;
  }

  /* Responsive for mobile */
  @media (max-width: 768px) {
    .appointment-actions {
      flex-direction: column;
      gap: 8px;
    }

    .detail-button-profile,
    .cancel-button-profile,
    .online-consultation-button-profile,
    .result-button-profile,
    .rate-service-btn {
      width: 100%;
      max-width: none;
      margin-bottom: 0;
      margin-right: 0;
    }
  }

  /* Modal responsive */
  @media (max-width: 768px) {
    .appointment-detail-modal {
      margin: 8px;
      max-width: calc(100vw - 16px);
    }

    .appointment-detail-modal .ant-modal {
      margin: 0;
      max-width: 100%;
    }

    .appointment-detail-modal .ant-modal-content {
      margin: 0;
    }
  }

  .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .detail-label {
    min-width: auto;
  }

  .detail-value {
    text-align: left;
  }

  .detail-section {
    padding: 16px;
  }

  .service-detail-item {
    padding: 12px;
  }

  .appointment-info p {
    margin: 2px 0;
    color: #4b5563;
    font-size: 12px;
  }

  .result-body {
    margin-bottom: 12px;
  }

  .result-body h3 {
    margin: 0 0 8px 0;
    color: #1f2937;
    font-size: 14px;
    font-weight: 600;
    padding: 4px 0;
    border-bottom: 1px solid #e5e7eb;
  }

  .medical-profile-details {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .result-item {
    display: flex;
    flex-direction: column;
    gap: 2px;
    padding: 6px 8px;
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 4px;
    margin-bottom: 4px;
  }

  .result-label {
    font-weight: 600;
    color: #374151;
    font-size: 12px;
    margin-bottom: 2px;
  }

  .result-value {
    color: #1f2937;
    font-size: 12px;
    line-height: 1.4;
    white-space: pre-wrap;
    word-wrap: break-word;
    background: #ffffff;
    padding: 4px 6px;
    border-radius: 3px;
    border: 1px solid #d1d5db;
    min-height: 16px;
  }

  .appointment-info p {
    font-size: 11px;
    margin: 1px 0;
  }

  .result-body {
    margin-bottom: 8px;
  }

  .result-body h3 {
    font-size: 13px;
    margin-bottom: 6px;
    padding: 3px 0;
  }

  .medical-profile-details {
    gap: 3px;
  }

  .result-item {
    padding: 4px 6px;
    margin-bottom: 3px;
  }

  .result-label {
    font-size: 11px;
  }

  .result-value {
    font-size: 11px;
    padding: 3px 4px;
  }
}
