@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes scaleIn {
  from { 
    opacity: 0;
    transform: scale(0.95); 
  }
  to { 
    opacity: 1;
    transform: scale(1); 
  }
}

/* --- OVERLAY & MODAL STRUCTURE --- */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(17, 24, 39, 0.6);
  backdrop-filter: blur(4px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out forwards;
}

.modal-content {
  background: #fff;
  padding: 2.5rem 2.5rem 2rem 2.5rem;
  border-radius: 2rem;
  max-width: 900px;      /* Tăng chiều rộng modal */
  width: 98vw;
  box-shadow: 0 16px 40px -8px rgba(79,70,229,0.13), 0 4px 16px -4px rgba(0,0,0,0.06);
  animation: scaleIn 0.3s ease-out forwards;
  position: relative;
  font-family: 'Inter', 'Segoe UI', sans-serif;
  border: 1.5px solid #e0e7ef;
}

/* Nút đóng (X) ở góc */
.modal-close-button {
  position: absolute;
  top: 1.2rem;
  right: 1.2rem;
  background: #f3f4f6;
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1.7rem;
  line-height: 1;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: 0 2px 8px rgba(0,0,0,0.07);
}
.modal-close-button:hover {
  background: #e0e7ef;
  color: #ef4444;
  transform: rotate(90deg) scale(1.08);
}


/* --- HEADER & SECTIONS --- */
.modal-content h2 {
  margin-top: 0;
  margin-bottom: 2rem;
  font-size: 1.6rem;
  font-weight: 700;
  color: #1f2937;
  text-align: center;
  border-bottom: 1.5px solid #e5e7eb;
  padding-bottom: 1.2rem;
  letter-spacing: 0.2px;
}

.modal-section {
  margin-bottom: 1.5rem;
}

.modal-section h4 {
  font-size: 1.08rem;
  font-weight: 600;
  color: #4b5563;
  margin-bottom: 1rem;
}
.period-start-option {
  background: #f9fafb;
  border: 1.5px solid #e5e7eb;
  border-radius: 14px;
  padding: 1.1rem 1rem;
  transition: all 0.2s;
}
.period-start-option:has(input:checked) {
  border-color: #fca5a5;
  background-color: #fef2f2;
}

.period-start-label {
  display: flex;
  align-items: center;
  font-size: 1.08rem;
  font-weight: 600;
  color: #374151;
  cursor: pointer;
}

.period-start-label input {
  width: 20px;
  height: 20px;
  margin-right: 14px;
  accent-color: #ef4444;
  border-radius: 6px;
  border: 2px solid #e5e7eb;
  transition: border 0.2s;
}
.period-start-label input:focus {
  outline: 2px solid #4f46e5;
}
.symptoms-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr); /* 4 cột đều nhau */
  gap: 18px;
  margin-top: 8px;
  width: 100%;
  justify-items: center; /* căn giữa các ô trong cột */
}

.symptom-label {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  border: 2px solid #e5e7eb;
  padding: 12px 10px;
  border-radius: 10px;
  cursor: pointer;
  background: #f8fafc;
  font-size: 1.08rem;
  font-weight: 500;
  color: #374151;
  transition: all 0.2s;
  box-shadow: 0 1px 4px rgba(0,0,0,0.03);
  min-height: 48px;
  width: 100%;
  max-width: 420px; /* Tự động điều chỉnh chiều rộng */
  white-space: nowrap;      /* Không cho xuống dòng */
  overflow: hidden;
  text-overflow: ellipsis;
}
.symptom-label:hover {
  border-color: #a5b4fc;
  background: #e0e7ff;
  color: #4f46e5;
}
.symptom-label:has(input:checked) {
  border-color: #3b82f6;
  background-color: #eff6ff;
  color: #1e40af;
  font-weight: 600;
}

.symptom-label {
  position: relative;
  /* ...các thuộc tính khác... */
}
.symptom-label span::before {
  content: '';
  display: inline-block;
  width: 18px;
  height: 18px;
  border: 2px solid #9ca3af;
  border-radius: 5px;
  margin-right: 14px;
  transition: all 0.2s;
  background: #fff;
}
.symptom-label:has(input:checked) span::before {
  background-color: #3b82f6;
  border-color: #3b82f6;
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e");
}
.symptom-label input[type="checkbox"] {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  opacity: 0;
  width: 24px;
  height: 24px;
  margin: 0;
  cursor: pointer;
  z-index: 2;
}
symptom-label span {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 1.08rem;
  font-weight: 500;
}

.symptom-label span .symptom-icon {
  font-size: 1.6em;
  margin-bottom: 2px;
}


/* --- ACTION BUTTONS --- */
.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1.5px solid #e5e7eb;
}

.button-primary, .button-secondary {
  padding: 13px 28px;
  border: none;
  border-radius: 10px;
  font-size: 1.08rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: 0 2px 8px rgba(79,70,229,0.07);
}

.button-primary {
  background-color: #4f46e5;
  color: white;
  border: 1.5px solid #4f46e5;
}
.button-secondary {
  background-color: white;
  color: #374151;
  border: 1.5px solid #d1d5db;
}

.button-primary:hover, .button-secondary:hover {
  transform: translateY(-2px) scale(1.04);
  box-shadow: 0 4px 16px rgba(79,70,229,0.13);
}
.button-secondary:hover {
  border-color: #a5b4fc;
  color: #4f46e5;
}
.period-day-info-card {
  background-color: #fee2e2;
  color: #b91c1c;
  border-radius: 10px;
  padding: 14px 18px;
  margin: 1rem 0;
  font-weight: 600;
  text-align: center;
  border: 1.5px solid #fca5a5;
  animation: fadeIn 0.3s ease;
  font-size: 1.08rem;
}

.button-danger {
  background-color: #fff0f0;
  color: #ef4444;
  border: 1.5px solid #ef4444;
  padding: 13px 18px;
  border-radius: 10px;
  font-size: 1.08rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.2s;
  margin-right: auto;
}
.button-danger:hover {
  background-color: #fee2e2;
  color: #b91c1c;
  border-color: #b91c1c;
}



@media (max-width: 600px) {
  .modal-content {
    padding: 1.2rem 0.2rem 1.2rem 0.2rem;
    max-width: 600px;
    width: 100vw;
  }
  .symptoms-grid {
    grid-template-columns: 1fr;
    gap: 10px;
  }
}