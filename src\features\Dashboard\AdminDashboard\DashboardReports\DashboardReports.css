/* Dashboard Reports Styles */
.dashboard-reports {
  padding: 0;
}

.dashboard-reports .ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dashboard-reports .ant-card-head {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border-bottom: none;
}

.dashboard-reports .ant-card-head-title {
  color: white;
  font-weight: 600;
}

.dashboard-reports .ant-card-extra {
  color: white;
}

.dashboard-reports .ant-card-extra .ant-btn {
  color: white;
  border-color: rgba(255, 255, 255, 0.3);
}

.dashboard-reports .ant-card-extra .ant-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
}

.dashboard-reports .ant-card-extra .ant-btn-primary {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}

.dashboard-reports .ant-card-extra .ant-btn-primary:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
}

.dashboard-reports .ant-card-extra .ant-select {
  color: white;
}

/* .dashboard-reports .ant-card-extra .ant-select .ant-select-selector {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.3);
  color: white;
} */

.dashboard-reports .ant-card-extra .ant-select .ant-select-arrow {
  color: white;
}

.dashboard-reports .ant-card-extra .ant-picker {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.3);
  color: white;
}

.dashboard-reports .ant-card-extra .ant-picker input {
  color: white;
}

.dashboard-reports .ant-card-extra .ant-picker .ant-picker-suffix {
  color: white;
}

/* Statistics Cards */
.stat-card {
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border: 1px solid #e8e8e8;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stat-card .ant-statistic-title {
  color: #666;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
}

.stat-card .ant-statistic-content {
  font-size: 24px;
  font-weight: 600;
}

/* Nested Cards */
.dashboard-reports .ant-card .ant-card {
  background: #ffffff;
  border: 1px solid #f0f0f0;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

.dashboard-reports .ant-card .ant-card-head {
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.dashboard-reports .ant-card .ant-card-head-title {
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.dashboard-reports .ant-card .ant-card-extra {
  color: #666;
}

.dashboard-reports .ant-card .ant-card-extra .ant-btn {
  color: #666;
  border-color: #d9d9d9;
}

.dashboard-reports .ant-card .ant-card-extra .ant-btn:hover {
  color: #1890ff;
  border-color: #1890ff;
  background: #f6ffed;
}

/* Tables */
.dashboard-reports .ant-table {
  font-size: 13px;
}

.dashboard-reports .ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
  color: #333;
  border-bottom: 2px solid #f0f0f0;
}

.dashboard-reports .ant-table-tbody > tr > td {
  border-bottom: 1px solid #f5f5f5;
}

.dashboard-reports .ant-table-tbody > tr:hover > td {
  background: #f0f9ff;
}

/* Progress Bars */
.dashboard-reports .ant-progress {
  margin-bottom: 16px;
}

.dashboard-reports .ant-progress-text {
  font-size: 12px;
  font-weight: 600;
}

/* Tags */
.dashboard-reports .ant-tag {
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  padding: 2px 8px;
}

/* Statistics in tables */
.dashboard-reports .ant-statistic {
  margin: 0;
}

.dashboard-reports .ant-statistic-content {
  font-size: 14px;
  font-weight: 600;
}

.dashboard-reports .ant-statistic-content-suffix {
  font-size: 12px;
  color: #666;
  margin-left: 4px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-reports .ant-card-extra {
    flex-direction: column;
    gap: 8px;
  }

  .dashboard-reports .ant-card-extra .ant-space {
    flex-wrap: wrap;
  }

  .stat-card .ant-statistic-content {
    font-size: 20px;
  }

  .dashboard-reports .ant-table {
    font-size: 12px;
  }

  .dashboard-reports .ant-card .ant-card-head-title {
    font-size: 14px;
  }
}

@media (max-width: 576px) {
  .dashboard-reports {
    padding: 0 8px;
  }

  .stat-card .ant-statistic-content {
    font-size: 18px;
  }

  .dashboard-reports .ant-card-extra .ant-btn {
    font-size: 12px;
    padding: 4px 8px;
  }

  .dashboard-reports .ant-select {
    min-width: 100px;
  }
}

/* Loading States */
.dashboard-reports .ant-spin-container {
  min-height: 200px;
}

/* Custom Divider */
.dashboard-reports .ant-divider {
  margin: 24px 0;
  border-color: #e8e8e8;
}

/* Icon Colors */
.dashboard-reports .anticon {
  font-size: 16px;
}

/* Card Body Padding */
.dashboard-reports .ant-card-body {
  padding: 20px;
}

.dashboard-reports .ant-card .ant-card-body {
  padding: 16px;
}

/* Space Components */
.dashboard-reports .ant-space {
  width: 100%;
}

.dashboard-reports .ant-space-item {
  display: flex;
  align-items: center;
}

/* Status Filter Dropdown Styling */
.dashboard-reports .ant-card .ant-card-extra .ant-select {
  background: #ffffff;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.dashboard-reports .ant-card .ant-card-extra .ant-select .ant-select-selector {
  background: #ffffff;
  border: none;
  color: #333;
  font-weight: 500;
  padding: 4px 11px;
  height: 24px;
}

.dashboard-reports
  .ant-card
  .ant-card-extra
  .ant-select
  .ant-select-selection-item {
  color: #333;
  font-size: 13px;
  line-height: 22px;
}

.dashboard-reports .ant-card .ant-card-extra .ant-select .ant-select-arrow {
  color: #666;
  font-size: 12px;
  transition: transform 0.3s ease;
}

.dashboard-reports
  .ant-card
  .ant-card-extra
  .ant-select.ant-select-open
  .ant-select-arrow {
  transform: rotate(180deg);
}

/* Dropdown Menu Styling */
.dashboard-reports .ant-select-dropdown {
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid #e8e8e8;
  padding: 4px 0;
}

.dashboard-reports .ant-select-item {
  padding: 8px 12px;
  font-size: 13px;
  font-weight: 500;
  color: #333;
  transition: all 0.2s ease;
}

.dashboard-reports .ant-select-item:hover {
  background: #f0f9ff;
  color: #1890ff;
}

.dashboard-reports .ant-select-item-option-selected {
  background: #e6f7ff;
  color: #1890ff;
  font-weight: 600;
}

.dashboard-reports .ant-select-item-option-selected:hover {
  background: #bae7ff;
}
