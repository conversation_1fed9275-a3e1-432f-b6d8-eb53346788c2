import api from "../../../../configs/api";

/**
 * API functions for User management
 */

// L<PERSON><PERSON> danh sách tất cả users từ tất cả các role
export const fetchUsers = async () => {
  try {
    const roles = ["CUSTOMER", "STAFF", "CONSULTANT"];
    const allUsers = [];

    // Gọi API cho từng role
    for (const role of roles) {
      try {
        const response = await api.get(`/admin/users?role=${role}`);
        const users = Array.isArray(response.data)
          ? response.data
          : [response.data];

        // Thêm field role vào mỗi user nếu chưa có
        const usersWithRole = users.map((user) => ({
          ...user,
          role: user.role || role,
        }));

        allUsers.push(...usersWithRole);
        console.log(` Fetched ${users.length} users with role ${role}`);
      } catch (roleError) {
        console.warn(
          ` Không thể lấy users với role ${role}:`,
          roleError.message
        );
      }
    }

    console.log(` Total fetched users: ${allUsers.length}`);
    console.log("Sample user data:", allUsers[0]); // Debug log
    return allUsers;
  } catch (error) {
    console.error(" Lỗi lấy danh sách users:", error);
    console.error("Error details:", error.response?.data);
    return [];
  }
};

// Thêm user mới
export const addUser = async (user) => {
  try {
    const response = await api.post("/admin/user", user);
    return response.data;
  } catch (error) {
    console.error("Lỗi thêm user:", error);
    throw error;
  }
};

// Cập nhật user
export const updateUser = async (id, user) => {
  try {
    const response = await api.put(`/admin/user/${id}`, user);
    return response.data;
  } catch (error) {
    console.error("Lỗi sửa user:", error);
    throw error;
  }
};

// Xóa user
export const deleteUser = async (id) => {
  try {
    await api.delete(`/admin/user/${id}`);
  } catch (error) {
    console.error("Lỗi xóa user:", error);
    throw error;
  }
};
