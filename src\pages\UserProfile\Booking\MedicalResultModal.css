.medical-result-container {
  padding: 12px;
  background-color: #f9f9f9;
}

.medical-result-header {
  text-align: center;
  margin-bottom: 20px;
  padding: 16px;
  background: white;
  border-radius: 6px;
}

.main-title {
  color: #1890ff !important;
  margin-bottom: 12px !important;
  font-weight: 600;
  text-align: center;
  font-size: 18px !important;
}

.section-card {
  border-radius: 4px;
  background: white;
  border: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 8px;
}

.section-card .ant-card-body {
  padding: 8px 12px !important;
}

.section-title {
  color: #262626 !important;
  margin-bottom: 6px !important;
  font-weight: 600;
  font-size: 16px !important;
}

.section-divider {
  margin: 8px 0 16px 0;
  border-color: #e8e8e8;
}

.info-item {
  margin-bottom: 12px;
  padding: 8px 0;
}

.info-label {
  display: block;
  color: #666;
  margin-bottom: 2px;
  font-size: 14px;
  font-weight: 600;
  line-height: 1.2;
}

.info-value {
  color: #262626;
  font-size: 14px;
  line-height: 1.3;
  display: block;
  margin-bottom: 4px;
  word-break: break-word;
}

.info-value-block {
  background-color: #fafafa;
  padding: 8px 12px;
  border-radius: 4px;
  border: none;
  margin-top: 4px;
}

.info-value-block .ant-typography {
  color: #262626;
  font-size: 14px;
  line-height: 1.4;
  margin-bottom: 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .medical-result-container {
    padding: 8px;
  }

  .section-card .ant-card-body {
    padding: 6px 8px !important;
  }

  .medical-result-header {
    padding: 12px;
    margin-bottom: 16px;
  }

  .main-title {
    font-size: 16px !important;
    margin-bottom: 8px !important;
  }

  .section-title {
    font-size: 14px !important;
  }

  .info-label,
  .info-value {
    font-size: 13px;
  }
}

@media (max-width: 576px) {
  .info-item {
    margin-bottom: 8px;
    padding: 6px 0;
  }

  .info-label {
    font-size: 13px;
  }

  .info-value,
  .info-value-block .ant-typography {
    font-size: 13px;
  }
}
