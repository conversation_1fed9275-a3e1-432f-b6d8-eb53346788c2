.staff-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

/* Tab styles */
.MuiTabs-root {
  border-bottom: 1px solid #e0e0e0;
}

.MuiTab-root {
  text-transform: none;
  font-weight: 500;
  min-width: 120px;
}

/* Paper styles */
.MuiPaper-root {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

/* List styles */
.MuiList-root {
  padding: 0;
}

.MuiListItem-root {
  padding: 16px;
  transition: background-color 0.2s;
}

.MuiListItem-root:hover {
  background-color: #f5f5f5;
}

/* Button styles */
.MuiButton-root {
  text-transform: none;
  font-weight: 500;
}

/* Dialog styles */
.MuiDialog-paper {
  min-width: 400px;
}

.MuiDialogContent-root {
  padding: 20px;
}

/* Grid styles */
.MuiGrid-item {
  padding: 16px;
}

/* Typography styles */
.MuiTypography-h4 {
  color: #1976d2;
  margin-bottom: 24px;
}

.MuiTypography-h6 {
  color: #333;
  font-weight: 600;
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .staff-container {
    padding: 16px;
  }

  .MuiDialog-paper {
    min-width: 300px;
  }
}

.ant-layout-header {
  padding: 0 20px;
}

.ant-layout-sider {
  background: #fff;
}

.ant-layout-content {
  margin: 24px 16px;
  padding: 24px;
  min-height: 280px;
}

/* Card styles */
.ant-card {
  margin-bottom: 16px;
}

.ant-card-head {
  border-bottom: 1px solid #f0f0f0;
}

/* Table styles */
.ant-table-wrapper {
  background: white;
  border-radius: 8px;
}

/* List styles */
.ant-list-item {
  padding: 16px;
}

/* Tag styles */
.ant-tag {
  margin-right: 8px;
}

/* Button styles */
.ant-btn {
  margin-right: 8px;
}

/* Form styles */
.ant-form-item {
  margin-bottom: 16px;
}

/* Modal styles */
.ant-modal-content {
  border-radius: 18px;
}

/* Tabs styles */
.ant-tabs-nav {
  margin-bottom: 16px;
}

.ant-tabs-tab {
  padding: 12px 16px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .ant-card {
    margin-bottom: 12px;
  }

  .ant-list-item {
    padding: 12px;
  }
}
